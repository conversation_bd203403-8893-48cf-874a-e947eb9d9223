#!/usr/bin/env python3
"""
演示事件循环生命周期问题
解释为什么start()中的事件循环在stop()时找不到
"""

import asyncio
import threading
import time


async def async_task(name: str, duration: float):
    """模拟异步任务"""
    print(f"  🚀 {name} 开始执行")
    await asyncio.sleep(duration)
    print(f"  ✅ {name} 执行完成")


def demo_event_loop_lifecycle():
    """演示事件循环的生命周期问题"""
    print("=" * 60)
    print("🔄 事件循环生命周期演示")
    print("=" * 60)
    
    print("\n📋 场景1: 模拟原始的start()和stop()方法")
    print("-" * 40)
    
    # 模拟start()方法
    def start_method():
        print("🟢 start() 方法开始")
        
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        print("  📦 创建并设置新的事件循环")
        
        # 运行异步任务
        print("  🏃 运行异步任务...")
        loop.run_until_complete(async_task("mid_server.start", 1.0))
        
        print("  ⚠️  run_until_complete() 执行完毕")
        print("  ⚠️  事件循环虽然存在，但已经'用完了'")
        print("🟢 start() 方法结束")
        
        return loop  # 返回循环引用
    
    # 模拟stop()方法 - 问题版本
    def stop_method_problematic():
        print("\n🔴 stop() 方法开始 (问题版本)")
        try:
            # 尝试获取当前事件循环
            current_loop = asyncio.get_event_loop()
            print("  ❌ 尝试获取当前事件循环...")
            print(f"  ❌ 循环状态: {current_loop}")
        except RuntimeError as e:
            print(f"  💥 错误: {e}")
            print("  💥 这就是 'There is no current event loop' 错误的来源!")
    
    # 模拟stop()方法 - 修复版本
    def stop_method_fixed(saved_loop):
        print("\n🟢 stop() 方法开始 (修复版本)")
        try:
            if saved_loop and not saved_loop.is_closed():
                print("  ✅ 使用保存的事件循环")
                saved_loop.run_until_complete(async_task("mid_server.stop", 0.5))
                saved_loop.close()
                print("  ✅ 事件循环正确关闭")
            else:
                print("  🔄 创建新的事件循环")
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                new_loop.run_until_complete(async_task("mid_server.stop", 0.5))
                new_loop.close()
                print("  ✅ 新事件循环正确关闭")
        except Exception as e:
            print(f"  ❌ 错误: {e}")
    
    # 执行演示
    saved_loop = start_method()
    stop_method_problematic()
    stop_method_fixed(saved_loop)


def demo_thread_context():
    """演示线程上下文对事件循环的影响"""
    print("\n" + "=" * 60)
    print("🧵 线程上下文对事件循环的影响")
    print("=" * 60)
    
    main_thread_loop = None
    
    def main_thread_work():
        nonlocal main_thread_loop
        print("🟢 主线程: 创建事件循环")
        main_thread_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(main_thread_loop)
        main_thread_loop.run_until_complete(async_task("主线程任务", 0.5))
        print("🟢 主线程: 任务完成")
    
    def signal_handler_simulation():
        print("\n🔴 信号处理器线程: 尝试获取事件循环")
        try:
            # 在不同线程中尝试获取事件循环
            current_loop = asyncio.get_event_loop()
            print(f"  ✅ 获取到循环: {current_loop}")
        except RuntimeError as e:
            print(f"  💥 错误: {e}")
            print("  💥 不同线程有不同的事件循环上下文!")
        
        # 使用保存的循环引用
        if main_thread_loop and not main_thread_loop.is_closed():
            print("  🔄 使用保存的循环引用")
            main_thread_loop.run_until_complete(async_task("信号处理器任务", 0.3))
            print("  ✅ 成功执行!")
    
    # 主线程执行
    main_thread_work()
    
    # 模拟信号处理器在不同线程中执行
    signal_thread = threading.Thread(target=signal_handler_simulation)
    signal_thread.start()
    signal_thread.join()


def demo_solution_comparison():
    """对比不同解决方案"""
    print("\n" + "=" * 60)
    print("⚖️  解决方案对比")
    print("=" * 60)
    
    print("\n❌ 方案1: 每次都获取当前循环 (容易出错)")
    print("```python")
    print("def stop(self):")
    print("    loop = asyncio.get_event_loop()  # 可能失败")
    print("    loop.run_until_complete(self.mid_server.stop())")
    print("```")
    
    print("\n⚠️  方案2: 每次都创建新循环 (资源浪费)")
    print("```python")
    print("def stop(self):")
    print("    loop = asyncio.new_event_loop()  # 总是创建新的")
    print("    asyncio.set_event_loop(loop)")
    print("    loop.run_until_complete(self.mid_server.stop())")
    print("    loop.close()")
    print("```")
    
    print("\n✅ 方案3: 保存循环引用 (推荐)")
    print("```python")
    print("def __init__(self):")
    print("    self._event_loop = None")
    print("")
    print("def start(self):")
    print("    self._event_loop = asyncio.new_event_loop()")
    print("    asyncio.set_event_loop(self._event_loop)")
    print("    self._event_loop.run_until_complete(self.mid_server.start())")
    print("")
    print("def stop(self):")
    print("    if self._event_loop and not self._event_loop.is_closed():")
    print("        self._event_loop.run_until_complete(self.mid_server.stop())")
    print("        self._event_loop.close()")
    print("    else:")
    print("        # 备选方案")
    print("        loop = asyncio.new_event_loop()")
    print("        # ...")
    print("```")


def main():
    """主函数"""
    demo_event_loop_lifecycle()
    demo_thread_context()
    demo_solution_comparison()
    
    print("\n" + "=" * 60)
    print("📝 总结")
    print("=" * 60)
    print("🔍 问题根源:")
    print("  1. run_until_complete() 执行完后，循环虽存在但已'用完'")
    print("  2. 不同线程有不同的事件循环上下文")
    print("  3. 信号处理器可能在不同线程中执行")
    print("")
    print("✅ 解决方案:")
    print("  1. 在start()时保存事件循环引用")
    print("  2. 在stop()时优先使用保存的循环")
    print("  3. 提供备选方案以确保健壮性")
    print("")
    print("🎯 这就是为什么修改后的main_new.py能解决问题的原因!")


if __name__ == "__main__":
    main()
