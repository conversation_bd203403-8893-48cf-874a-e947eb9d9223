import asyncio
from typing import Dict
from utils.flog import flogger
from strategy_mid_server.strategy_client import StrategyEngineClient
from config.config import StrategyEngineInfo


class HeartbeatCheckService:

    def __init__(self, strategy_engines_info: Dict[int, StrategyEngineInfo], strategy_clients: Dict[int, StrategyEngineClient]):
        self.strategy_engines_info = strategy_engines_info
        self.strategy_clients = strategy_clients
        self.running = False
        self.heartbeat_task: asyncio.Task = None

    async def start_heartbeat_check(self):
        if self.running:
            flogger.warning("heartbeat check service already running")
            return

        self.running = True
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        flogger.info("heartbeat check service started")

    async def stop_heartbeat_check(self):
        if not self.running:
            return

        self.running = False
        flogger.info("heartbeat check service stopped")
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            flogger.info("heartbeat check loop stopped")

    async def _heartbeat_loop(self):
        flogger.info("heartbeat check loop started")

        while self.running:
            try:
                await self._check_all_engines_heartbeat()
                await asyncio.sleep(20)
            except asyncio.CancelledError:
                flogger.info("heartbeat check loop cancelled")
                break
            except Exception as e:
                flogger.error("heartbeat check loop error", error=str(e))
                await asyncio.sleep(5)

        flogger.info("heartbeat check loop stopped")

    async def _check_all_engines_heartbeat(self):
        tasks = []
        for node_id, engine_info in self.strategy_engines_info.items():
            task = self._check_single_engine_heartbeat(engine_info)
            tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def _check_single_engine_heartbeat(self, engine_info: StrategyEngineInfo):
        try:
            client = self.strategy_clients.get(engine_info.node_id)
            is_healthy = await client.health_check()
            previous_status = engine_info.is_ready
            if is_healthy:
                if not previous_status:
                    engine_info.is_ready = True
                    flogger.info("strategy engine became ready",
                                 node_id=engine_info.node_id,
                                 url=engine_info.config.url)
                else:
                    flogger.debug("strategy engine heartbeat success",
                                  node_id=engine_info.node_id)
            else:
                if previous_status:
                    engine_info.is_ready = False
                    flogger.warning("strategy engine became unavailable",
                                    node_id=engine_info.node_id,
                                    url=engine_info.config.url)
                else:
                    flogger.debug("strategy engine heartbeat failed",
                                  node_id=engine_info.node_id)
        except Exception as e:
            previous_status = engine_info.is_ready
            engine_info.is_ready = False
            if previous_status:
                flogger.warning("strategy engine heartbeat exception, marked as unavailable",
                                node_id=engine_info.node_id,
                                url=engine_info.config.url,
                                error=str(e))
            else:
                flogger.debug("strategy engine heartbeat exception",
                              node_id=engine_info.node_id,
                              error=str(e))
