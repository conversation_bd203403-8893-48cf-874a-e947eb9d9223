import httpx
from typing import List
from common.models import *
from common.strategy_engine_web_route import *
from utils.flog import flogger


class StrategyEngineClient:

    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)

    async def get_strategy_parameters(self) -> List[StrategyParamMsg]:
        strategy_parameters_data: List[StrategyParamMsg] = []
        try:
            url = f"{self.base_url}{GET_STRATEGY_PARAMETERS}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.get(url)
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to get strategy parameters",
                              message=api_response.message, request_id=api_response.request_id)
                return strategy_parameters_data
            if not api_response.data:
                flogger.warning(
                    "empty data returned from strategy engine", request_id=api_response.request_id)
                return strategy_parameters_data
            for item in api_response.data:
                strategy_parameters_data.append(StrategyParamMsg(**item))
            flogger.info("get strategy parameters",
                         strategy_parameters=strategy_parameters_data,
                         request_id=api_response.request_id)
            return strategy_parameters_data
        except Exception as e:
            flogger.error("failed to get strategy parameters", error=str(e))
            return strategy_parameters_data

    async def get_strategy_enums(self) -> List[StrategyEnumMsg]:
        """预留接口"""
        pass

    async def get_deployed_strategies(self) -> List[StrategyDeployMsg]:
        deploy_strategy_data: List[StrategyDeployMsg] = []
        try:
            url = f"{self.base_url}{GET_DEPLOYED_STRATEGIES}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.get(url)
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to get deployed strategies",
                              message=api_response.message, request_id=api_response.request_id)
                return deploy_strategy_data
            if not api_response.data:
                flogger.warning(
                    "empty data returned from strategy engine", request_id=api_response.request_id)
                return deploy_strategy_data
            for item in api_response.data:
                deploy_strategy_data.append(StrategyDeployMsg(**item))
            flogger.info("get strategy deploy info",
                         strategy_deploy_info=deploy_strategy_data,
                         request_id=api_response.request_id)
            return deploy_strategy_data
        except Exception as e:
            flogger.error("failed to get strategy deploy info", error=str(e))
            return deploy_strategy_data

    async def get_strategy_instances(self) -> List[StrategyInstanceMsg]:
        strategy_instances_data: List[StrategyInstanceMsg] = []
        try:
            url = f"{self.base_url}{GET_ALL_STRATEGY_INSTANCES}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.get(url)
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to get strategy instances",
                              message=api_response.message, request_id=api_response.request_id)
                return strategy_instances_data
            if not api_response.data:
                flogger.warning(
                    "empty data returned from strategy engine", request_id=api_response.request_id)
                return strategy_instances_data
            for item in api_response.data:
                strategy_instances_data.append(StrategyInstanceMsg(**item))
            flogger.info("get strategy instances",
                         strategy_instances=strategy_instances_data,
                         request_id=api_response.request_id)
            return strategy_instances_data
        except Exception as e:
            flogger.error("failed to get strategy instances", error=str(e))
            return strategy_instances_data

    async def get_strategy_instance_parameters(self) -> List[StrategyInstanceParamMsg]:
        strategy_instance_parameters_data: List[StrategyInstanceParamMsg] = []
        try:
            url = f"{self.base_url}{GET_STRATEGY_INSTANCE_PARAMETERS}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.get(url)
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to get strategy instance parameters",
                              message=api_response.message, request_id=api_response.request_id)
                return strategy_instance_parameters_data
            if not api_response.data:
                flogger.warning(
                    "empty data returned from strategy engine", request_id=api_response.request_id)
                return strategy_instance_parameters_data
            for item in api_response.data:
                strategy_instance_parameters_data.append(
                    StrategyInstanceParamMsg(**item))
            flogger.info("get strategy instance parameters",
                         strategy_instance_parameters=strategy_instance_parameters_data,
                         request_id=api_response.request_id)
            return strategy_instance_parameters_data
        except Exception as e:
            flogger.error(
                "failed to get strategy instance parameters", error=str(e))
            return strategy_instance_parameters_data

    async def load_strategy(self, strategy_name: str) -> ApiResponseModel:
        """预留接口"""
        pass

    async def create_strategy_instance(self, strategy_instance: StrategyInstanceMsg) -> ApiResponseModel:
        try:
            url = f"{self.base_url}{CREATE_STRATEGY_INSTANCE}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.post(url, json=strategy_instance.model_dump(mode='json'))
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to create strategy instance",
                              message=api_response.message, request_id=api_response.request_id)
                return api_response
            flogger.info("create strategy instance",
                         strategy_instance=strategy_instance,
                         request_id=api_response.request_id)
            return api_response
        except Exception as e:
            flogger.error("failed to create strategy instance", error=str(e))
            return ApiResponseModel(success=False, message=str(e))

    async def start_strategy_instance(self, strategy_instance_id: int) -> ApiResponseModel:
        try:
            url = f"{self.base_url}{START_STRATEGY_INSTANCE}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.post(url, json={"strategy_instance_id": strategy_instance_id})
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to start strategy instance",
                              message=api_response.message, request_id=api_response.request_id)
                return api_response
            flogger.info("start strategy instance",
                         strategy_instance_id=strategy_instance_id,
                         request_id=api_response.request_id)
            return api_response
        except Exception as e:
            flogger.error("failed to start strategy instance", error=str(e))
            return ApiResponseModel(success=False, message=str(e))

    async def stop_strategy_instance(self, strategy_instance_id: int) -> ApiResponseModel:
        try:
            url = f"{self.base_url}{STOP_STRATEGY_INSTANCE}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.post(url, json={"strategy_instance_id": strategy_instance_id})
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to stop strategy instance",
                              message=api_response.message, request_id=api_response.request_id)
                return api_response
            flogger.info("stop strategy instance",
                         strategy_instance_id=strategy_instance_id,
                         request_id=api_response.request_id)
            return api_response
        except Exception as e:
            flogger.error("failed to stop strategy instance", error=str(e))
            return ApiResponseModel(success=False, message=str(e))

    async def delete_strategy_instance(self, strategy_instance_id: int) -> ApiResponseModel:
        try:
            url = f"{self.base_url}{DELETE_STRATEGY_INSTANCE}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.post(url, json={"strategy_instance_id": strategy_instance_id})
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to delete strategy instance",
                              message=api_response.message, request_id=api_response.request_id)
                return api_response
            flogger.info("delete strategy instance",
                         strategy_instance_id=strategy_instance_id,
                         request_id=api_response.request_id)
            return api_response
        except Exception as e:
            flogger.error("failed to delete strategy instance", error=str(e))
            return ApiResponseModel(success=False, message=str(e))

    async def update_strategy_instance_parameter(self, strategy_instance_param: StrategyInstanceParamMsg) -> ApiResponseModel:
        try:
            url = f"{self.base_url}{UPDATE_STRATEGY_INSTANCE_PARAMETER}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.post(url, json=strategy_instance_param.model_dump(mode='json'))
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to update strategy instance parameter",
                              message=api_response.message, request_id=api_response.request_id)
                return api_response
            flogger.info("update strategy instance parameter",
                         strategy_instance_param=strategy_instance_param,
                         request_id=api_response.request_id)
            return api_response
        except Exception as e:
            flogger.error(
                "failed to update strategy instance parameter", error=str(e))
            return ApiResponseModel(success=False, message=str(e))

    async def modify_instance_priority(self, strategy_instance: StrategyInstanceMsg) -> ApiResponseModel:
        try:
            url = f"{self.base_url}{MODIFY_INSTANCE_PRIORITY}"
            flogger.debug("send request to strategy engine", url=url)
            response = await self.client.post(url, json=strategy_instance.model_dump(mode='json'))
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            if not api_response.success:
                flogger.error("failed to modify instance priority",
                              message=api_response.message, request_id=api_response.request_id)
                return api_response
            flogger.info("modify instance priority",
                         strategy_instance=strategy_instance,
                         request_id=api_response.request_id)
            return api_response
        except Exception as e:
            flogger.error("failed to modify instance priority", error=str(e))
            return ApiResponseModel(success=False, message=str(e))

    async def health_check(self) -> bool:
        try:
            url = f"{self.base_url}{HEALTH_CHECK}"
            response = await self.client.get(url, timeout=10.0)
            response.raise_for_status()
            api_response_data = response.json()
            api_response = ApiResponseModel(**api_response_data)
            return api_response.success
        except Exception as e:
            flogger.debug("health check failed", url=url, error=str(e))
            return False

    async def close(self):
        await self.client.aclose()
