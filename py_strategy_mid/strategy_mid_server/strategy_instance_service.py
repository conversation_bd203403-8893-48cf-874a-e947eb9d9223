from typing import List, Dict
from common.enum import *
from common.table_enum import table_enum
from common.models import *
from utils.rule_checker import <PERSON><PERSON><PERSON><PERSON>
from utils.flog import flogger
from strategy_mid_server.strategy_client import StrategyEngineClient
from config.config import StrategyEngineInfo
from common.mdb import MemoryDatabase
import httpx


class StrategyInstanceService:

    def __init__(self, mdb: MemoryDatabase, strategy_engines_info: Dict[int, StrategyEngineInfo], strategy_clients: Dict[int, StrategyEngineClient]):
        self.mdb = mdb
        self.strategy_engines_info = strategy_engines_info
        self.strategy_clients = strategy_clients

    async def handle_create_strategy_instance(self, strategy_instance: StrategyInstanceMsg) -> HttpResponseModel:
        try:
            result: HttpDataItem = None
            strategy_client = self.strategy_clients.get(
                strategy_instance.strategy_engine_id)
            api_response = await strategy_client.create_strategy_instance(strategy_instance)
            error_response = RuleChecker.check_api_response_success(api_response, "create strategy instance")
            if error_response:
                return error_response
            if api_response.data:
                strategy_instance.strategy_instance_id = api_response.data.get(
                    'strategy_instance_id')
                self.mdb.update_record(
                    table_enum.py_strategy_instance_field, strategy_instance)
                flogger.info("updated strategy instance to mdb",
                             strategy_instance_id=strategy_instance.strategy_instance_id)
                result = HttpDataItem(
                    name=table_enum.py_strategy_instance_field.name,
                    value=strategy_instance.model_dump(mode='json')
                )
            return RuleChecker.create_success_response([result] if result else [])
        except httpx.TimeoutException as e:
            flogger.error("handle create strategy instance failed",
                          strategy_instance=strategy_instance,
                          error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error("handle create strategy instance failed",
                          strategy_instance=strategy_instance,
                          error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def handle_start_strategy_instance(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        if len(instance_ids) == 1:
            return await self._handle_start_strategy_instance_single(trading_account_id, instance_ids[0])
        else:
            return await self._handle_start_strategy_instance_batch(trading_account_id, instance_ids)

    async def _handle_start_strategy_instance_single(self, trading_account_id: int, instance_id: int) -> HttpResponseModel:
        try:
            instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                table_enum.py_strategy_instance_field,
                strategy_instance_id=instance_id
            )
            strategy_client = self.strategy_clients.get(
                instance_record.strategy_engine_id)
            api_response = await strategy_client.start_strategy_instance(instance_id)
            error_response = RuleChecker.check_api_response_success(api_response, f"start strategy instance {instance_id}")
            if error_response:
                return error_response
            instance_record.strategy_instance_state = StrategyStateEnum.STRATEGY_RUNNING_STAT
            self.mdb.update_record(
                table_enum.py_strategy_instance_field, instance_record)
            http_data_item = HttpDataItem(
                name=table_enum.py_strategy_instance_field.name,
                value=instance_record.model_dump(mode='json')
            )
            flogger.info("updated strategy instance to mdb",
                         strategy_instance_id=instance_record.strategy_instance_id)
            return RuleChecker.create_success_response([http_data_item])
        except httpx.TimeoutException as e:
            flogger.error("handle start strategy instance single failed",
                          strategy_instance_id=instance_id,
                          error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error("handle start strategy instance single failed",
                          strategy_instance_id=instance_id,
                          error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def _handle_start_strategy_instance_batch(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        try:
            all_results: List[HttpDataItem] = []
            failed_instance_ids: List[int] = []
            success_instance_ids: List[int] = []

            for instance_id in instance_ids:
                try:
                    instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                        table_enum.py_strategy_instance_field,
                        strategy_instance_id=instance_id
                    )
                    strategy_client = self.strategy_clients.get(
                        instance_record.strategy_engine_id)
                    api_response = await strategy_client.start_strategy_instance(instance_id)
                    if not api_response.success:
                        flogger.error("start strategy instance API failed",
                                      strategy_instance_id=instance_id)
                        failed_instance_ids.append(instance_id)
                        continue
                    instance_record.strategy_instance_state = StrategyStateEnum.STRATEGY_RUNNING_STAT
                    self.mdb.update_record(
                        table_enum.py_strategy_instance_field, instance_record)
                    http_data_item = HttpDataItem(
                        name=table_enum.py_strategy_instance_field.name,
                        value=instance_record.model_dump(mode='json')
                    )
                    all_results.append(http_data_item)
                    success_instance_ids.append(instance_id)
                    flogger.info("updated strategy instance to mdb",
                                 strategy_instance_id=instance_record.strategy_instance_id)
                except Exception as e:
                    flogger.error("failed to process single strategy instance",
                                  strategy_instance_id=instance_id,
                                  error=str(e))
                    failed_instance_ids.append(instance_id)
                    continue
            if failed_instance_ids:
                flogger.warning("some strategy instances failed to start",
                                failed_instance_ids=failed_instance_ids, success_instance_ids=success_instance_ids)
                return RuleChecker.get_request_not_all_success_response()
            else:
                return RuleChecker.create_success_response(all_results)
        except httpx.TimeoutException as e:
            flogger.error(
                "handle start strategy instance failed", error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error(
                "handle start strategy instance failed", error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def handle_delete_strategy_instance(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        if len(instance_ids) == 1:
            return await self._handle_delete_strategy_instance_single(trading_account_id, instance_ids[0])
        else:
            return await self._handle_delete_strategy_instance_batch(trading_account_id, instance_ids)

    async def _handle_delete_strategy_instance_single(self, trading_account_id: int, instance_id: int) -> HttpResponseModel:
        try:
            instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                table_enum.py_strategy_instance_field,
                strategy_instance_id=instance_id
            )
            strategy_client = self.strategy_clients.get(
                instance_record.strategy_engine_id)
            api_response = await strategy_client.delete_strategy_instance(instance_id)
            error_response = RuleChecker.check_api_response_success(api_response, f"delete strategy instance {instance_id}")
            if error_response:
                return error_response
            instance_record.strategy_instance_state = StrategyStateEnum.STRATEGY_DELETE_STAT
            self.mdb.update_record(
                table_enum.py_strategy_instance_field, instance_record)
            http_data_item = HttpDataItem(
                name=table_enum.py_strategy_instance_field.name,
                value=instance_record.model_dump(mode='json')
            )
            flogger.info("updated strategy instance to DELETE state",
                         strategy_instance_id=instance_record.strategy_instance_id)
            return RuleChecker.create_success_response([http_data_item])
        except httpx.TimeoutException as e:
            flogger.error("handle delete strategy instance single failed",
                          strategy_instance_id=instance_id,
                          error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error("handle delete strategy instance single failed",
                          strategy_instance_id=instance_id,
                          error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def _handle_delete_strategy_instance_batch(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        try:
            all_results: List[HttpDataItem] = []
            failed_instance_ids: List[int] = []
            success_instance_ids: List[int] = []

            for instance_id in instance_ids:
                try:
                    instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                        table_enum.py_strategy_instance_field,
                        strategy_instance_id=instance_id
                    )
                    strategy_client = self.strategy_clients.get(
                        instance_record.strategy_engine_id)
                    api_response = await strategy_client.delete_strategy_instance(instance_id)
                    if not api_response.success:
                        flogger.error("delete strategy instance API failed",
                                      strategy_instance_id=instance_id)
                        failed_instance_ids.append(instance_id)
                        continue
                    instance_record.strategy_instance_state = StrategyStateEnum.STRATEGY_DELETE_STAT
                    self.mdb.update_record(
                        table_enum.py_strategy_instance_field, instance_record)
                    http_data_item = HttpDataItem(
                        name=table_enum.py_strategy_instance_field.name,
                        value=instance_record.model_dump(mode='json')
                    )
                    all_results.append(http_data_item)
                    success_instance_ids.append(instance_id)
                    flogger.info("updated strategy instance to DELETE state",
                                 strategy_instance_id=instance_record.strategy_instance_id)
                except Exception as e:
                    flogger.error("failed to process single strategy instance delete",
                                  strategy_instance_id=instance_id,
                                  error=str(e))
                    failed_instance_ids.append(instance_id)
                    continue
            if failed_instance_ids:
                flogger.warning("some strategy instances failed to delete",
                                failed_instance_ids=failed_instance_ids, success_instance_ids=success_instance_ids)
                return RuleChecker.get_request_not_all_success_response()
            else:
                return RuleChecker.create_success_response(all_results)
        except httpx.TimeoutException as e:
            flogger.error(
                "handle delete strategy instance failed", error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error(
                "handle delete strategy instance failed", error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def handle_pause_strategy_instance(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        if len(instance_ids) == 1:
            return await self._handle_pause_strategy_instance_single(trading_account_id, instance_ids[0])
        else:
            return await self._handle_pause_strategy_instance_batch(trading_account_id, instance_ids)

    async def _handle_pause_strategy_instance_single(self, trading_account_id: int, instance_id: int) -> HttpResponseModel:
        try:
            instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                table_enum.py_strategy_instance_field,
                strategy_instance_id=instance_id
            )
            strategy_client = self.strategy_clients.get(
                instance_record.strategy_engine_id)
            api_response = await strategy_client.stop_strategy_instance(instance_id)
            error_response = RuleChecker.check_api_response_success(api_response, f"pause strategy instance {instance_id}")
            if error_response:
                return error_response
            instance_record.strategy_instance_state = StrategyStateEnum.STRATEGY_PAUSE_STAT
            self.mdb.update_record(
                table_enum.py_strategy_instance_field, instance_record)
            http_data_item = HttpDataItem(
                name=table_enum.py_strategy_instance_field.name,
                value=instance_record.model_dump(mode='json')
            )
            flogger.info("updated strategy instance to PAUSE state",
                         strategy_instance_id=instance_record.strategy_instance_id)
            return RuleChecker.create_success_response([http_data_item])
        except httpx.TimeoutException as e:
            flogger.error("handle pause strategy instance single failed",
                          strategy_instance_id=instance_id,
                          error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error("handle pause strategy instance single failed",
                          strategy_instance_id=instance_id,
                          error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def _handle_pause_strategy_instance_batch(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        try:
            all_results: List[HttpDataItem] = []
            failed_instance_ids: List[int] = []
            success_instance_ids: List[int] = []

            for instance_id in instance_ids:
                try:
                    instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                        table_enum.py_strategy_instance_field,
                        strategy_instance_id=instance_id
                    )
                    strategy_client = self.strategy_clients.get(
                        instance_record.strategy_engine_id)
                    api_response = await strategy_client.stop_strategy_instance(instance_id)
                    error_response = RuleChecker.check_api_response_success(
                        api_response,
                        f"pause strategy instance {instance_id}"
                    )
                    if error_response:
                        flogger.error("pause strategy instance API failed",
                                      strategy_instance_id=instance_id)
                        failed_instance_ids.append(instance_id)
                        continue
                    instance_record.strategy_instance_state = StrategyStateEnum.STRATEGY_PAUSE_STAT
                    self.mdb.update_record(
                        table_enum.py_strategy_instance_field, instance_record)
                    http_data_item = HttpDataItem(
                        name=table_enum.py_strategy_instance_field.name,
                        value=instance_record.model_dump(mode='json')
                    )
                    all_results.append(http_data_item)
                    success_instance_ids.append(instance_id)
                    flogger.info("updated strategy instance to PAUSE state",
                                 strategy_instance_id=instance_record.strategy_instance_id)

                except Exception as e:
                    flogger.error("failed to process single strategy instance pause",
                                  strategy_instance_id=instance_id,
                                  error=str(e))
                    failed_instance_ids.append(instance_id)
                    continue
            if failed_instance_ids:
                flogger.warning("some strategy instances failed to pause",
                                failed_instance_ids=failed_instance_ids, success_instance_ids=success_instance_ids)
                return RuleChecker.get_request_not_all_success_response()
            else:
                return RuleChecker.create_success_response(all_results)
        except httpx.TimeoutException as e:
            flogger.error(
                "handle pause strategy instance failed", error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error(
                "handle pause strategy instance failed", error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def handle_update_instance_param(self, params: List[StrategyInstanceParamMsg]) -> HttpResponseModel:
        try:
            all_results: List[HttpDataItem] = []
            for param in params:
                instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                    table_enum.py_strategy_instance_field,
                    strategy_instance_id=param.strategy_instance_id
                )
                strategy_client = self.strategy_clients.get(
                    instance_record.strategy_engine_id)
                api_response = await strategy_client.update_strategy_instance_parameter(param)
                error_response = RuleChecker.check_api_response_success(
                    api_response,
                    f"update strategy instance parameter {param.strategy_instance_id}"
                )
                if error_response:
                    continue
                self.mdb.update_record(
                    table_enum.py_strategy_instance_param_field, param)
                http_data_item = HttpDataItem(
                    name=table_enum.py_strategy_instance_param_field.name,
                    value=param.model_dump(mode='json')
                )
                all_results.append(http_data_item)
                flogger.info("updated strategy instance parameter to mdb",
                             strategy_instance_id=param.strategy_instance_id,
                             param_key=param.param_key,
                             param_value=param.param_value)

            return RuleChecker.create_success_response(all_results)
        except httpx.TimeoutException as e:
            flogger.error("handle update instance param failed", error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error("handle update instance param failed", error=str(e))
            return RuleChecker.get_unknown_error_response()

    async def handle_modify_instance_priority(self, strategy_instance: StrategyInstanceMsg) -> HttpResponseModel:
        try:
            result: HttpDataItem = None
            instance_record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                table_enum.py_strategy_instance_field,
                strategy_instance_id=strategy_instance.strategy_instance_id
            )
            strategy_client = self.strategy_clients.get(
                instance_record.strategy_engine_id)

            api_response = await strategy_client.modify_instance_priority(strategy_instance)

            error_response = RuleChecker.check_api_response_success(
                api_response,
                f"modify strategy instance priority {strategy_instance.strategy_instance_id}"
            )
            if error_response:
                return error_response
            self.mdb.update_record(
                table_enum.py_strategy_instance_field, strategy_instance)
            result = HttpDataItem(
                name=table_enum.py_strategy_instance_field.name,
                value=strategy_instance.model_dump(mode='json')
            )
            return RuleChecker.create_success_response([result])
        except httpx.TimeoutException as e:
            flogger.error(
                "handle modify instance priority failed", error=str(e))
            return RuleChecker.get_request_timeout_response()
        except Exception as e:
            flogger.error(
                "handle modify instance priority failed", error=str(e))
            return RuleChecker.get_unknown_error_response()
