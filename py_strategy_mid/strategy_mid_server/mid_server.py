from typing import List, Dict, Union
from common.enum import *
from common.error import Error<PERSON>odeEnum, ErrorMsg
from common.table_manager import init_tables
from strategy_mid_server.strategy_client import StrategyEngineClient
from strategy_mid_server.websocket_server import WebSocketServer
from strategy_mid_server.data_fetch_service import DataFetchService
from strategy_mid_server.heartbeat_check_service import HeartbeatCheckService
from strategy_mid_server.websocket_publisher import WebsocketPublisher
from strategy_mid_server.strategy_instance_service import StrategyInstanceService
from utils.flog import flogger
from common.mdb import MemoryDatabase
from config.config import StrategyEngineInfo, StrategyEngineNode
from common.table_enum import table_enum
from common.models import *
import asyncio


class StrategyMidServer:

    def __init__(self,
                 mdb: MemoryDatabase,
                 strategy_engines: List[StrategyEngineNode]):

        flogger.info("init strategy mid server",
                     strategy_engines=[f"node_{node.node_id}({node.url})" for node in strategy_engines])
        self.mdb = mdb
        self._init_tables()

        self.strategy_engines_info: Dict[int, StrategyEngineInfo] = {}
        self.strategy_clients: Dict[int, StrategyEngineClient] = {}
        self._init_strategy_engines_info(strategy_engines)

        self.data_fetch_service = DataFetchService(mdb, self.strategy_clients)
        self.websocket_server = WebSocketServer()
        self.websocket_server.set_mdb(mdb)
        self.strategy_instance_handler = StrategyInstanceService(
            mdb, self.strategy_engines_info, self.strategy_clients)
        self.heartbeat_check_service = HeartbeatCheckService(
            self.strategy_engines_info, self.strategy_clients)
        self.websocket_publisher = WebsocketPublisher(
            mdb, self.websocket_server)

        self.running = False
        self._initial_fetch_done = False

        flogger.info("strategy mid server init complete",
                     strategy_engines_info=self.strategy_engines_info)

    def _init_tables(self):
        init_tables(self.mdb)

    def _init_strategy_engines_info(self, strategy_engines: List[StrategyEngineNode]):
        for engine_node in strategy_engines:
            strategy_engine_info = StrategyEngineInfo(
                node_id=engine_node.node_id,
                config=engine_node
            )
            self.strategy_engines_info[engine_node.node_id] = strategy_engine_info
            client = StrategyEngineClient(engine_node.url)
            self.strategy_clients[engine_node.node_id] = client
            flogger.info("created strategy engine client",
                         node_id=engine_node.node_id, url=engine_node.url)

    async def start(self):
        if self.running:
            flogger.warning("strategy mid server already running")
            return
        self.running = True
        flogger.info("starting strategy mid server")

        if not self._initial_fetch_done:
            await self._initial_data_fetch()
            self._initial_fetch_done = True

        await self.heartbeat_check_service.start_heartbeat_check()

    async def stop(self):
        if not self.running:
            return

        self.running = False
        flogger.info("stopping strategy mid server")

        await self.heartbeat_check_service.stop_heartbeat_check()

        try:
            await self.websocket_server.close_all_connections()
        except Exception as e:
            flogger.error("close websocket connections error", error=str(e))
        for node_id, client in self.strategy_clients.items():
            try:
                await client.close()
                flogger.debug(f"closed strategy engine client node_{node_id}")
            except Exception as e:
                flogger.error(
                    f"close strategy engine client node_{node_id} error", error=str(e))

        flogger.info("strategy mid server stopped")

    async def _initial_data_fetch(self):
        try:
            flogger.info(
                "starting initial data fetch from all strategy engines")
            await self.data_fetch_service.fetch_all_strategy_data()
            flogger.info("initial data fetch completed successfully")
        except Exception as e:
            flogger.error("initial data fetch failed", error=str(e))

    def get_websocket_server(self) -> WebSocketServer:
        return self.websocket_server

    def get_table_publisher_info(self) -> TablePublisherResponseModel:
        table_names = [table.name.replace('_field', '')
                       for table in table_enum]

        return TablePublisherResponseModel(
            service_type="config",
            tables=table_names
        )

    async def handle_create_strategy_instance(self, strategy_instance: StrategyInstanceMsg) -> HttpResponseModel:
        return await self.strategy_instance_handler.handle_create_strategy_instance(strategy_instance)

    async def handle_start_strategy_instance(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        return await self.strategy_instance_handler.handle_start_strategy_instance(trading_account_id, instance_ids)

    async def handle_delete_strategy_instance(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        return await self.strategy_instance_handler.handle_delete_strategy_instance(trading_account_id, instance_ids)

    async def handle_pause_strategy_instance(self, trading_account_id: int, instance_ids: List[int]) -> HttpResponseModel:
        return await self.strategy_instance_handler.handle_pause_strategy_instance(trading_account_id, instance_ids)

    async def handle_update_instance_param(self, params: List[StrategyInstanceParamMsg]) -> HttpResponseModel:
        return await self.strategy_instance_handler.handle_update_instance_param(params)

    async def handle_modify_instance_priority(self, strategy_instance: StrategyInstanceMsg) -> HttpResponseModel:
        return await self.strategy_instance_handler.handle_modify_instance_priority(strategy_instance)

    async def publish_strategy_instance(self, strategy_instance_ids: Union[int, List[int]]):
        await self.websocket_publisher.publish_strategy_instance(strategy_instance_ids)
    
    async def publish_strategy_instance_param(self, strategy_instance_id: int):
        await self.websocket_publisher.publish_strategy_instance_param(strategy_instance_id)