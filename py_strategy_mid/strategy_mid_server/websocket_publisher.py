from typing import List, Union
from common.enum import *
from common.table_enum import table_enum
from common.models import *
from utils.data_converter import DataConverter
from utils.flog import flogger
from common.mdb import MemoryDatabase
from strategy_mid_server.websocket_server import WebSocketServer


class WebsocketPublisher:

    def __init__(self, mdb: MemoryDatabase, websocket_server: WebSocketServer) -> None:
        self.mdb = mdb
        self.websocket_server = websocket_server

    async def publish_strategy_instance(self, strategy_instance_ids: Union[int, List[int]]):
        try:
            if isinstance(strategy_instance_ids, int):
                instance_ids = [strategy_instance_ids]
            else:
                instance_ids = strategy_instance_ids

            all_records: List[StrategyInstanceMsg] = []
            for instance_id in instance_ids:
                record: StrategyInstanceMsg = self.mdb.get_records_by_params(
                    table_enum.py_strategy_instance_field,
                    strategy_instance_id=instance_id
                )
                if record:
                    all_records.append(record)
                else:
                    flogger.warning("strategy instance not found for websocket push",
                                    strategy_instance_id=instance_id)
            if all_records:
                ws_msg = DataConverter.convert_by_table_type(
                    table_enum.py_strategy_instance_field,
                    all_records,
                    WebSocketMsgTypeEnum.DATA
                )
                await self.websocket_server.publish(ws_msg)
                flogger.info("websocket pushed batch",
                             strategy_instance_ids=[
                                 r.strategy_instance_id for r in all_records],
                             count=len(all_records))
        except Exception as e:
            flogger.error("websocket push failed", error=str(e))

    async def publish_strategy_instance_param(self, strategy_instance_id: int):
        try:
            all_param_records: List[StrategyInstanceParamMsg] = self.mdb.get_all_records(
                table_enum.py_strategy_instance_param_field)
            filtered_records: List[StrategyInstanceParamMsg] = []
            if all_param_records:
                for record in all_param_records:
                    if record.strategy_instance_id == strategy_instance_id:
                        filtered_records.append(record)
            if filtered_records:
                ws_msg = DataConverter.convert_by_table_type(
                    table_enum.py_strategy_instance_param_field,
                    filtered_records,
                    WebSocketMsgTypeEnum.DATA
                )
                await self.websocket_server.publish(ws_msg)
                flogger.info("websocket pushed strategy instance param batch",
                             strategy_instance_id=strategy_instance_id,
                             param_count=len(filtered_records),
                             param_keys=[r.param_key for r in filtered_records])
            else:
                flogger.warning("no strategy instance param found for websocket push",
                                strategy_instance_id=strategy_instance_id)
        except Exception as e:
            flogger.error("websocket push strategy instance param failed",
                          strategy_instance_id=strategy_instance_id,
                          error=str(e))
