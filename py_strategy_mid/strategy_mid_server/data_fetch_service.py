import asyncio
from typing import List, Dict
from common.table_enum import table_enum
from common.mdb import MemoryDatabase
from strategy_mid_server.strategy_client import StrategyEngineClient
from utils.flog import flogger


class DataFetchService:

    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(DataFetchService, cls).__new__(cls)
        return cls._instance

    def __init__(self, mdb: MemoryDatabase = None, strategy_clients: Dict[int, StrategyEngineClient] = None):
        if self._initialized:
            return

        if mdb is None or strategy_clients is None:
            raise ValueError(
                "DataFetchService requires mdb and strategy_clients for first initialization")

        self.mdb = mdb
        self.strategy_clients = strategy_clients
        self._initialized = True

    @classmethod
    def get_instance(cls) -> 'DataFetchService':
        if cls._instance is None:
            raise RuntimeError(
                "DataFetchService not initialized. Call DataFetchService(mdb, strategy_clients) first.")
        return cls._instance

    @classmethod
    def is_initialized(cls) -> bool:
        return cls._instance is not None and cls._instance._initialized

    def get_data_fetch_configs(self):
        return [
            {
                'name': 'strategy_deploy',
                'table_enum': table_enum.py_strategy_deploy_field,
                'client_method': 'get_deployed_strategies',
                'data_key': 'deploy'
            },
            {
                'name': 'strategy_parameters',
                'table_enum': table_enum.py_strategy_param_field,
                'client_method': 'get_strategy_parameters',
                'data_key': 'param'
            },
            {
                'name': 'strategy_instances',
                'table_enum': table_enum.py_strategy_instance_field,
                'client_method': 'get_strategy_instances',
                'data_key': 'instance'
            },
            {
                'name': 'strategy_instance_parameters',
                'table_enum': table_enum.py_strategy_instance_param_field,
                'client_method': 'get_strategy_instance_parameters',
                'data_key': 'instance_param'
            },
        ]

    def _store_data_to_mdb(self, table_name: str, records: List):
        if not records:
            flogger.debug("no data to store to mdb", table=table_name)
            return
        try:
            for record in records:
                self.mdb.update_record(table_name, record)
            flogger.debug("store data to mdb complete",
                          table=table_name, count=len(records))
        except Exception as e:
            flogger.error("store data to mdb error",
                          table=table_name, error=str(e), records=records)

    async def fetch_all_strategy_data(self):
        fetch_configs = self.get_data_fetch_configs()
        await self.fetch_all_data_types(fetch_configs)

    async def fetch_all_data_types(self, fetch_configs: list):
        fetch_tasks = []
        for config in fetch_configs:
            task = self.fetch_data_by_type(config)
            fetch_tasks.append(task)

        await asyncio.gather(*fetch_tasks, return_exceptions=True)

    async def fetch_data_by_type(self, config: dict):
        data_name = config['name']
        table_enum_value = config['table_enum']
        client_method = config['client_method']
        data_key = config['data_key']

        try:
            tasks = []
            for node_id, client in self.strategy_clients.items():
                task = self.fetch_single_data_type_from_engine(
                    client, node_id, client_method, data_key)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            all_data = []
            for result in results:
                if isinstance(result, Exception):
                    flogger.error(
                        f"fetch {data_name} data from engine error", error=str(result))
                elif result:
                    all_data.extend(result)

            if all_data:
                self._store_data_to_mdb(table_enum_value, all_data)
                flogger.info(f"fetched {data_name} data", count=len(all_data))
            else:
                flogger.info(
                    f"no {data_name} data returned from any strategy engine")

            return all_data

        except Exception as e:
            flogger.error(f"fetch {data_name} data error", error=str(e))
            return []

    async def fetch_single_data_type_from_engine(self, client: StrategyEngineClient, node_id: int, method_name: str, data_key: str):
        try:
            method = getattr(client, method_name)
            data = await method()

            result = data or []
            flogger.debug(
                f"fetched {data_key} data from node_{node_id}", count=len(result))
            return result

        except Exception as e:
            flogger.error(
                f"fetch {data_key} data from node_{node_id} error", error=str(e))
            raise e

    async def refresh_table_data_by_enum(self, table_enum_value: table_enum):
        try:
            fetch_configs = self.get_data_fetch_configs()

            matching_config = None
            for config in fetch_configs:
                if config['table_enum'] == table_enum_value:
                    matching_config = config
                    break

            if matching_config:
                flogger.info("refreshing table data", table=table_enum_value.name,
                             config_name=matching_config['name'])
                fresh_data = await self.fetch_data_by_type(matching_config)
                flogger.debug("table data refresh completed", table=table_enum_value.name, count=len(
                    fresh_data) if fresh_data else 0)
                return fresh_data or []
            else:
                flogger.debug(
                    "table not in fetch configs, skip refresh", table=table_enum_value.name)
                return []

        except Exception as e:
            flogger.error("refresh table data error",
                          table=table_enum_value.name, error=str(e))
            return []
