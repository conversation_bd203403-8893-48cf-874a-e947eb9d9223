import json
from typing import Dict, Set, List
from fastapi import WebSocket, WebSocketDisconnect
from common.enum import WebSocketMsgTypeEnum
from common.models import WebSocketDataResponseModel, WebSocketOpResponseModel
from common.table_enum import table_enum, get_table_enum, get_table_model_class
from utils.flog import flogger
from utils.data_converter import DataConverter
from utils.model_field_extractor import get_pydantic_model_fields_string
from common.mdb import MemoryDatabase


class ConnectionManager:

    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.subscriptions: Dict[WebSocket, Set[table_enum]] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.subscriptions[websocket] = set()
        flogger.info("websocket client connected",
                     client_count=len(self.active_connections))

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.subscriptions:
            del self.subscriptions[websocket]
        flogger.info("websocket client disconnected",
                     client_count=len(self.active_connections))

    async def publish_message(self, message: WebSocketDataResponseModel):
        if not self.active_connections:
            return

        # 将WebSocket表名转换为table_enum
        table_enum_value = get_table_enum(message.table)
        if not table_enum_value:
            flogger.warning("unknown table name", table=message.table)
            return
        if message.type == WebSocketMsgTypeEnum.SNAPSHOT.value:
            message.is_snap_last = 1
        message_text = json.dumps(message.model_dump(
            exclude_none=True), ensure_ascii=False)
        disconnected_connections = []

        for connection in self.active_connections:
            try:
                if table_enum_value in self.subscriptions.get(connection, set()):
                    await connection.send_text(message_text)
            except Exception as e:
                flogger.error("publish message to client error", error=str(e))
                disconnected_connections.append(connection)

        for connection in disconnected_connections:
            self.disconnect(connection)

        flogger.debug("publish message to clients", table=message.table,
                      client_count=len(self.active_connections))

    def subscribe_table(self, websocket: WebSocket, table: str):
        if websocket in self.subscriptions:
            table_enum_value = get_table_enum(table)
            if table_enum_value:
                self.subscriptions[websocket].add(table_enum_value)
                subscribed_table_names = [
                    t.name for t in self.subscriptions[websocket]]
                flogger.info("client subscribe table", table=table,
                             subscribed_tables=subscribed_table_names)
                return table_enum_value
            else:
                flogger.warning(
                    "unknown table name for subscription", table=table)
        return None

    def unsubscribe_table(self, websocket: WebSocket, table: str):
        table_enum_value = get_table_enum(table)
        if websocket in self.subscriptions and table_enum_value and table_enum_value in self.subscriptions[websocket]:
            self.subscriptions[websocket].remove(table_enum_value)
            subscribed_table_names = [
                t.name for t in self.subscriptions[websocket]]
            flogger.info("client unsubscribe table", table=table,
                         subscribed_tables=subscribed_table_names)
        else:
            flogger.warning(
                "table not subscribed or unknown table name", table=table)


class WebSocketServer:

    def __init__(self):
        self.manager = ConnectionManager()
        self.mdb: MemoryDatabase = None

    def set_mdb(self, mdb: MemoryDatabase):
        self.mdb = mdb

    async def websocket_endpoint(self, websocket: WebSocket):
        await self.manager.connect(websocket)

        try:
            while True:
                data = await websocket.receive_text()
                await self._handle_client_message(websocket, data)
        except WebSocketDisconnect:
            self.manager.disconnect(websocket)
        except Exception as e:
            flogger.error("websocket handle error", error=str(e))
            self.manager.disconnect(websocket)

    async def _handle_client_message(self, websocket: WebSocket, message: str):
        try:
            if message.strip() == "heartbeat":
                await websocket.send_text("heartbeat")
                flogger.debug("recive and reply heartbeat")
                return

            data = json.loads(message)
            op_field = data.get("op")
            table_field = data.get("table")

            if op_field and table_field:
                if op_field == WebSocketMsgTypeEnum.SUBSCRIBE.value:
                    table_field_with_suffix = f"{table_field}_field"
                    table_enum_value = self.manager.subscribe_table(
                        websocket, table_field_with_suffix)
                    await self._send_subscription_response(websocket, table_field_with_suffix, table_enum_value)
                    if table_enum_value:
                        await self._send_table_snapshot(websocket, table_enum_value)
                elif op_field == WebSocketMsgTypeEnum.UNSUBSCRIBE.value:
                    table_field_with_suffix = f"{table_field}_field"
                    self.manager.unsubscribe_table(websocket, table_field_with_suffix)
                else:
                    flogger.warning("unknown operation type",
                                    op=op_field, message=message)
            else:
                flogger.warning(
                    "unknown client message format", message=message)

        except json.JSONDecodeError:
            flogger.error("client message json decode error", message=message)
        except Exception as e:
            flogger.error("handle client message error",
                          error=str(e), message=message)

    async def publish(self, message: WebSocketDataResponseModel):
        await self.manager.publish_message(message)

    def get_connection_count(self) -> int:
        return len(self.manager.active_connections)

    async def close_all_connections(self):
        if not self.manager.active_connections:
            return

        flogger.info("closing all websocket connections",
                     count=len(self.manager.active_connections))

        # 复制连接列表以避免在迭代时修改
        connections_to_close = self.manager.active_connections.copy()

        for websocket in connections_to_close:
            try:
                await websocket.close()
            except Exception as e:
                flogger.error(
                    "failed to close websocket connection", error=str(e))

        self.manager.active_connections.clear()
        self.manager.subscriptions.clear()

        flogger.info("all websocket connections closed")

    async def _send_table_snapshot(self, websocket: WebSocket, table_enum_value: table_enum):
        try:
            if not self.mdb:
                flogger.debug("mdb not set, send empty snapshot")
                ws_rsp = WebSocketDataResponseModel(type=WebSocketMsgTypeEnum.SNAPSHOT.value,
                                                table=table_enum_value.name, data=[], is_snap_last=1)
                message_text = json.dumps(ws_rsp.model_dump(
                    exclude_none=True), ensure_ascii=False)
                await websocket.send_text(message_text)
                return

            mdb_data = self.mdb.get_all_records(table_enum_value)

            if not mdb_data:
                flogger.info("no data in table for snapshot",
                             table=table_enum_value.name)
                ws_rsp = WebSocketDataResponseModel(type=WebSocketMsgTypeEnum.SNAPSHOT.value,
                                                table=table_enum_value.name, data=[], is_snap_last=1)
            else:
                ws_rsp = DataConverter.convert_by_table_type(
                    table_enum_value, mdb_data, WebSocketMsgTypeEnum.SNAPSHOT)
                if not ws_rsp:
                    ws_rsp = WebSocketDataResponseModel(
                        type=WebSocketMsgTypeEnum.SNAPSHOT.value, table=table_enum_value.name, data=[], is_snap_last=1)
                    flogger.warning(
                        "failed to convert data for snapshot", table=table_enum_value.name)

            message_text = json.dumps(ws_rsp.model_dump(
                exclude_none=True), ensure_ascii=False)
            await websocket.send_text(message_text)

            flogger.info("sent table snapshot to client",
                         table=table_enum_value.name,
                         record_count=len(ws_rsp.data))

        except Exception as e:
            flogger.error("send table snapshot error",
                          table=table_enum_value.name,
                          error=str(e))

    async def _send_subscription_response(self, websocket: WebSocket, table_name: str, table_enum_value: table_enum = None):
        """发送订阅操作响应"""
        try:
            if table_enum_value:
                # 表存在，获取模型字段名
                model_class = get_table_model_class(table_enum_value)
                if model_class:
                    header = get_pydantic_model_fields_string(model_class)
                else:
                    header = ""

                # 创建成功响应
                op_response = WebSocketOpResponseModel(
                    op="sub",
                    table=table_name,
                    code=0,
                    message="success",
                    header=header
                )

                flogger.info("sent subscription success response to client",
                             table=table_name, header=header)
            else:
                # 表不存在，创建失败响应
                op_response = WebSocketOpResponseModel(
                    op="sub",
                    table=table_name,
                    code=-1,
                    message="table not found",
                    header=""
                )

                flogger.warning("sent subscription failure response to client",
                                table=table_name, reason="table not found")

            # 发送响应
            message_text = json.dumps(op_response.model_dump(exclude_none=True), ensure_ascii=False)
            await websocket.send_text(message_text)

        except Exception as e:
            flogger.error("send subscription response error",
                          table=table_name, error=str(e))