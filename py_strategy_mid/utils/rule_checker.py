from typing import Dict, Optional
from common.error import Error<PERSON>ode<PERSON>num, ErrorMsg
from common.models import HttpResponseModel, ApiResponseModel
from utils.flog import flogger


class RuleChecker:

    ERROR_UNKNOWN_RESPONSE = None
    ERROR_INVALID_PARAM_RESPONSE = None
    ERROR_NO_STRATEGY_INSTANCE_RESPONSE = None
    ERROR_STRATEGY_INSTANCE_ACTION_FAILED_RESPONSE = None
    ERROR_REQUEST_NOT_ALL_SUCCESS_RESPONSE = None
    ERROR_REQUEST_TIMEOUT_RESPONSE = None

    @classmethod
    def _init_common_responses(cls):
        if cls.ERROR_UNKNOWN_RESPONSE is None:
            cls.ERROR_UNKNOWN_RESPONSE = cls.create_error_response(
                ErrorCodeEnum.ERROR_UNKNOWN)
            cls.ERROR_INVALID_PARAM_RESPONSE = cls.create_error_response(
                ErrorCodeEnum.ERROR_INVALID_PARAM)
            cls.ERROR_NO_STRATEGY_INSTANCE_RESPONSE = cls.create_error_response(
                ErrorCodeEnum.ERROR_NO_STRATEGY_INSTANCE)
            cls.ERROR_STRATEGY_INSTANCE_ACTION_FAILED_RESPONSE = cls.create_error_response(
                ErrorCodeEnum.ERROR_STRATEGY_INSTANCE_ACTION_FAILED)
            cls.ERROR_REQUEST_NOT_ALL_SUCCESS_RESPONSE = cls.create_error_response(
                ErrorCodeEnum.ERROR_REQUEST_NOT_ALL_SUCCESS)
            cls.ERROR_REQUEST_TIMEOUT_RESPONSE = cls.create_error_response(
                ErrorCodeEnum.ERROR_REQUEST_TIMEOUT)

    @classmethod
    def get_unknown_error_response(cls) -> HttpResponseModel:
        cls._init_common_responses()
        return cls.ERROR_UNKNOWN_RESPONSE

    @classmethod
    def get_invalid_param_response(cls) -> HttpResponseModel:
        cls._init_common_responses()
        return cls.ERROR_INVALID_PARAM_RESPONSE

    @classmethod
    def get_no_strategy_instance_response(cls) -> HttpResponseModel:
        cls._init_common_responses()
        return cls.ERROR_NO_STRATEGY_INSTANCE_RESPONSE

    @classmethod
    def get_strategy_instance_action_failed_response(cls) -> HttpResponseModel:
        cls._init_common_responses()
        return cls.ERROR_STRATEGY_INSTANCE_ACTION_FAILED_RESPONSE

    @classmethod
    def get_request_not_all_success_response(cls) -> HttpResponseModel:
        cls._init_common_responses()
        return cls.ERROR_REQUEST_NOT_ALL_SUCCESS_RESPONSE

    @classmethod
    def get_request_timeout_response(cls) -> HttpResponseModel:
        cls._init_common_responses()
        return cls.ERROR_REQUEST_TIMEOUT_RESPONSE

    @staticmethod
    def create_error_response(error_code: ErrorCodeEnum) -> HttpResponseModel:
        return HttpResponseModel(
            code=error_code.value,
            message=ErrorMsg.get_error_msg(error_code)
        )

    @staticmethod
    def create_success_response(data: list = None) -> HttpResponseModel:
        return HttpResponseModel(
            code=ErrorCodeEnum.ERROR_NONE.value,
            message=ErrorMsg.get_error_msg(ErrorCodeEnum.ERROR_NONE),
            data=data or []
        )

    @staticmethod
    def check_api_response_success(api_response: ApiResponseModel,
                                   operation_name: str = "operation") -> Optional[HttpResponseModel]:

        if not api_response.success:
            flogger.error(f"{operation_name} failed",
                          message=api_response.message,
                          request_id=api_response.request_id)
            return RuleChecker.get_strategy_instance_action_failed_response()
        return None
