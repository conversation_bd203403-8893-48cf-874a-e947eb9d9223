from typing import Type, Dict, List
from pydantic import BaseModel


class ModelFieldExtractor:
    """Pydantic模型字段提取器"""
    
    def __init__(self):
        self._model_cache: Dict[Type[BaseModel], List[str]] = {}
    
    def get_model_fields(self, model_class: Type[BaseModel]) -> List[str]:
        """
        从Pydantic模型自动提取字段名
        
        Args:
            model_class: Pydantic模型类
            
        Returns:
            字段名列表
        """
        # 使用缓存避免重复计算
        if model_class in self._model_cache:
            return self._model_cache[model_class]
        
        fields = self._extract_fields(model_class)
        self._model_cache[model_class] = fields
        return fields
    
    def get_model_fields_string(self, model_class: Type[BaseModel]) -> str:
        """
        获取模型字段名的逗号分隔字符串
        
        Args:
            model_class: Pydantic模型类
            
        Returns:
            用逗号分隔的字段名字符串
        """
        fields = self.get_model_fields(model_class)
        return ','.join(fields)
    
    def _extract_fields(self, model_class: Type[BaseModel]) -> List[str]:
        """
        提取Pydantic模型的字段名
        兼容Pydantic v1和v2
        
        Args:
            model_class: Pydantic模型类
            
        Returns:
            字段名列表
        """
        if not issubclass(model_class, BaseModel):
            raise ValueError(f"{model_class} is not a Pydantic BaseModel")
        
        # Pydantic v2
        if hasattr(model_class, 'model_fields'):
            return list(model_class.model_fields.keys())
        
        # Pydantic v1
        elif hasattr(model_class, '__fields__'):
            return list(model_class.__fields__.keys())
        
        else:
            raise ValueError(f"Cannot extract fields from {model_class}")
    
    def clear_cache(self):
        """清空缓存"""
        self._model_cache.clear()


# 全局实例
_extractor = ModelFieldExtractor()


def get_pydantic_model_fields(model_class: Type[BaseModel]) -> List[str]:
    """
    获取Pydantic模型的字段名列表
    
    Args:
        model_class: Pydantic模型类
        
    Returns:
        字段名列表
    """
    return _extractor.get_model_fields(model_class)


def get_pydantic_model_fields_string(model_class: Type[BaseModel]) -> str:
    """
    获取Pydantic模型字段名的逗号分隔字符串
    
    Args:
        model_class: Pydantic模型类
        
    Returns:
        用逗号分隔的字段名字符串
    """
    return _extractor.get_model_fields_string(model_class)


def clear_model_fields_cache():
    """清空模型字段缓存"""
    _extractor.clear_cache()


# 为了方便使用，提供一些常用的模型字段提取函数
def extract_strategy_enum_fields():
    """提取策略枚举模型字段"""
    try:
        from ..common.models import StrategyEnumMsg
        return get_pydantic_model_fields(StrategyEnumMsg)
    except ImportError:
        return []


def extract_strategy_param_fields():
    """提取策略参数模型字段"""
    try:
        from ..common.models import StrategyParamMsg
        return get_pydantic_model_fields(StrategyParamMsg)
    except ImportError:
        return []


def extract_strategy_deploy_fields():
    """提取策略部署模型字段"""
    try:
        from ..common.models import StrategyDeployMsg
        return get_pydantic_model_fields(StrategyDeployMsg)
    except ImportError:
        return []


def extract_strategy_instance_fields():
    """提取策略实例模型字段"""
    try:
        from ..common.models import StrategyInstanceMsg
        return get_pydantic_model_fields(StrategyInstanceMsg)
    except ImportError:
        return []


def extract_strategy_instance_param_fields():
    """提取策略实例参数模型字段"""
    try:
        from ..common.models import StrategyInstanceParamMsg
        return get_pydantic_model_fields(StrategyInstanceParamMsg)
    except ImportError:
        return []


# 提供一个统一的字段提取映射
def get_all_model_fields_mapping():
    """
    获取所有模型的字段映射
    
    Returns:
        模型类到字段列表的映射字典
    """
    mapping = {}
    
    try:
        from ..common.models import (
            StrategyEnumMsg, StrategyParamMsg, StrategyDeployMsg,
            StrategyInstanceMsg, StrategyInstanceParamMsg
        )
        
        mapping[StrategyEnumMsg] = get_pydantic_model_fields(StrategyEnumMsg)
        mapping[StrategyParamMsg] = get_pydantic_model_fields(StrategyParamMsg)
        mapping[StrategyDeployMsg] = get_pydantic_model_fields(StrategyDeployMsg)
        mapping[StrategyInstanceMsg] = get_pydantic_model_fields(StrategyInstanceMsg)
        mapping[StrategyInstanceParamMsg] = get_pydantic_model_fields(StrategyInstanceParamMsg)
        
    except ImportError:
        pass
    
    return mapping


if __name__ == "__main__":
    # 测试代码
    print("Testing ModelFieldExtractor...")
    
    try:
        from common.models import StrategyDeployMsg
        
        fields = get_pydantic_model_fields(StrategyDeployMsg)
        print(f"StrategyDeployMsg fields: {fields}")
        
        fields_str = get_pydantic_model_fields_string(StrategyDeployMsg)
        print(f"StrategyDeployMsg fields string: {fields_str}")
        
    except ImportError as e:
        print(f"Import error: {e}")
