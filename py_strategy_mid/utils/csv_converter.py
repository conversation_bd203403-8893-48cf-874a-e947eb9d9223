from typing import Type, TypeVar, Dict, Any, List
from pydantic import BaseModel
from common.models import StrategyInstanceMsg, StrategyInstanceParamMsg, StrategyDeployMsg, StrategyParamMsg, StrategyEnumMsg
from common.enum import StrategyStateEnum, StrategyPauseReasonEnum, IntStatusEnum
from utils.flog import flogger

T = TypeVar('T', bound=BaseModel)


class CSVConverterMeta(type):
    """CSV转换器元类，用于自动生成转换配置"""

    def __new__(cls, name, bases, namespace):
        # 定义模型转换配置
        model_configs = {
            StrategyInstanceMsg: {
                'fields': [
                    ('strategy_instance_id', int),
                    ('strategy_instance_name', str),
                    ('strategy_name', str),
                    ('strategy_engine_id', int),
                    ('user_id', int),
                    ('strategy_instance_state', StrategyStateEnum),
                    ('trading_account_id', int),
                    ('strategy_instance_priority', int),
                    ('last_operator_id', int),
                    ('strategy_pause_reason', StrategyPauseReasonEnum)
                ],
                'doc': '策略实例CSV转换\n格式: strategy_instance_id,strategy_instance_name,strategy_name,strategy_engine_id,user_id,strategy_instance_state,trading_account_id,strategy_instance_priority,last_operator_id,strategy_pause_reason'
            },
            StrategyInstanceParamMsg: {
                'fields': [
                    ('strategy_instance_id', int),
                    ('strategy_engine_id', int),
                    ('param_key', str),
                    ('param_value', str),
                    ('last_operator_id', int)
                ],
                'doc': '策略实例参数CSV转换\n格式: strategy_instance_id,strategy_engine_id,param_key,param_value,last_operator_id'
            },
            StrategyDeployMsg: {
                'fields': [
                    ('strategy_name', str),
                    ('strategy_engine_id', int),
                    ('strategy_version', str),
                    ('status', IntStatusEnum),
                    ('last_operator_id', int)
                ],
                'doc': '策略部署CSV转换\n格式: strategy_name,strategy_engine_id,strategy_version,status,last_operator_id'
            },
            StrategyParamMsg: {
                'fields': [
                    ('strategy_name', str),
                    ('param_key', str),
                    ('param_type', str),
                    ('param_value', str),
                    ('index', int),
                    ('tick', float),
                    ('status', IntStatusEnum)
                ],
                'doc': '策略参数CSV转换\n格式: strategy_name,param_key,param_type,param_value,index,tick,status'
            },
            StrategyEnumMsg: {
                'fields': [
                    ('strategy_name', str),
                    ('enum_name', str),
                    ('enum_value', str),
                    ('status', IntStatusEnum)
                ],
                'doc': '策略枚举CSV转换\n格式: strategy_name,enum_name,enum_value,status'
            }
        }

        # 存储配置供运行时使用
        namespace['_model_configs'] = model_configs

        return super().__new__(cls, name, bases, namespace)


class CSVConverter(metaclass=CSVConverterMeta):
    """通用CSV转换器"""

    @classmethod
    def parse_csv_to_model(cls, csv_data: str, model_class: Type[T]) -> T:
        """将CSV数据转换为指定的Pydantic模型对象

        Args:
            csv_data: CSV格式的字符串数据
            model_class: 目标Pydantic模型类

        Returns:
            转换后的模型对象

        Raises:
            ValueError: 当CSV数据格式不正确或转换失败时
        """
        try:
            # 获取模型配置
            config = cls._model_configs.get(model_class)
            if not config:
                raise ValueError(f"不支持的模型类型: {model_class.__name__}")

            # 分割CSV数据
            fields = csv_data.strip().split(',')
            expected_field_count = len(config['fields'])

            if len(fields) != expected_field_count:
                raise ValueError(
                    f"CSV数据字段数量不正确，期望{expected_field_count}个字段，实际{len(fields)}个")

            # 构建模型参数字典
            model_kwargs = {}
            for i, (field_name, field_type) in enumerate(config['fields']):
                raw_value = fields[i].strip()

                try:
                    # 根据字段类型转换值
                    if field_type == str:
                        converted_value = raw_value
                    elif field_type == int:
                        converted_value = int(raw_value)
                    elif field_type == float:
                        converted_value = float(raw_value)
                    elif issubclass(field_type, BaseModel):
                        # 如果是Pydantic模型，递归转换
                        converted_value = field_type(**{field_name: raw_value})
                    elif hasattr(field_type, '__members__'):
                        # 如果是枚举类型
                        converted_value = field_type(raw_value)
                    else:
                        # 其他类型直接使用原始值
                        converted_value = raw_value

                    model_kwargs[field_name] = converted_value

                except (ValueError, TypeError) as e:
                    raise ValueError(
                        f"字段 '{field_name}' 转换失败: {raw_value} -> {field_type.__name__}, 错误: {str(e)}")

            # 创建模型对象
            model_instance = model_class(**model_kwargs)

            flogger.debug("csv converted to model",
                          csv_data=csv_data,
                          model_class=model_class.__name__,
                          model_instance=model_instance)

            return model_instance

        except Exception as e:
            flogger.error("csv to model conversion failed",
                          csv_data=csv_data,
                          model_class=model_class.__name__,
                          error=str(e))
            raise ValueError(f"CSV转换为{model_class.__name__}失败: {str(e)}")

    @classmethod
    def get_supported_models(cls) -> List[Type[BaseModel]]:
        """获取支持的模型类型列表"""
        return list(cls._model_configs.keys())

    @classmethod
    def get_model_csv_format(cls, model_class: Type[BaseModel]) -> str:
        """获取指定模型的CSV格式说明"""
        config = cls._model_configs.get(model_class)
        if config:
            return config['doc']
        return f"不支持的模型类型: {model_class.__name__}"

    @classmethod
    def get_model_field_info(cls, model_class: Type[BaseModel]) -> List[tuple]:
        """获取指定模型的字段信息"""
        config = cls._model_configs.get(model_class)
        if config:
            return config['fields']
        return []
