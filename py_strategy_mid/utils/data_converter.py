from typing import List, Dict, Callable, Any
from common.enum import WebSocketMsgTypeEnum
from common.models import StrategyDeployMsg, StrategyParamMsg, StrategyEnumMsg, StrategyInstanceMsg, StrategyInstanceParamMsg, WebSocketDataResponseModel
from common.table_enum import table_enum, get_ws_table_name
from utils.flog import flogger


class DataConverterMeta(type):
    """数据转换器元类，用于自动生成转换方法"""

    def __new__(cls, name, bases, namespace):
        # 定义字段映射配置
        field_configs = {
            table_enum.py_strategy_enum_field: {
                'model_type': StrategyEnumMsg,
                'fields': ['strategy_name', 'enum_name', 'enum_value', 'status'],
                'doc': '转换策略枚举字段数据\n格式: strategy_name,enum_name,enum_value,status'
            },
            table_enum.py_strategy_deploy_field: {
                'model_type': StrategyDeployMsg,
                'fields': ['strategy_name', 'strategy_engine_id', 'strategy_version', 'status', 'last_operator_id'],
                'doc': '转换策略部署字段数据\n格式: strategy_name,strategy_engine_id,strategy_version,status,last_operator_id'
            },
            table_enum.py_strategy_param_field: {
                'model_type': StrategyParamMsg,
                'fields': ['strategy_name', 'param_key', 'param_type', 'param_value', 'index', 'tick', 'status'],
                'doc': '转换策略参数字段数据\n格式: strategy_name,param_key,param_type,param_value,index,tick,status'
            },
            table_enum.py_strategy_instance_field: {
                'model_type': StrategyInstanceMsg,
                'fields': ['strategy_instance_id', 'strategy_name', 'strategy_engine_id', 'strategy_instance_name',
                           'user_id', 'strategy_instance_state', 'trading_account_id', 'strategy_instance_priority',
                           'last_operator_id', 'strategy_pause_reason'],
                'doc': '转换策略实例字段数据\n格式: strategy_instance_id,strategy_name,strategy_engine_id,strategy_instance_name,user_id,strategy_instance_state,trading_account_id,strategy_instance_priority,last_operator_id,strategy_pause_reason'
            },
            table_enum.py_strategy_instance_param_field: {
                'model_type': StrategyInstanceParamMsg,
                'fields': ['strategy_instance_id', 'strategy_engine_id', 'param_key', 'param_value', 'last_operator_id'],
                'doc': '转换策略实例参数字段数据\n格式: strategy_instance_id,strategy_engine_id,param_key,param_value,last_operator_id'
            }
        }

        # 为每个表生成转换方法
        for table_type, config in field_configs.items():
            method_name = f"convert_{table_type.name}"
            method = cls._create_converter_method(table_type, config)
            method.__doc__ = config['doc']
            namespace[method_name] = staticmethod(method)

        # 存储配置供运行时使用
        namespace['_field_configs'] = field_configs

        return super().__new__(cls, name, bases, namespace)

    @classmethod
    def _create_converter_method(cls, table_type: table_enum, config: Dict[str, Any]) -> Callable:
        """创建转换方法"""
        def converter_method(strategy_data: List[Any], msg_type: WebSocketMsgTypeEnum) -> WebSocketDataResponseModel:
            if not strategy_data:
                flogger.warning(f"empty data for {table_type.name}")
                return None

            data_list = []
            fields = config['fields']

            for item in strategy_data:
                # 使用model_dump(mode='json')获取JSON兼容的字典
                if hasattr(item, 'model_dump'):
                    item_dict = item.model_dump(mode='json')
                else:
                    # 如果不是Pydantic模型，直接使用原对象
                    item_dict = item

                # 动态获取字段值
                field_values = []
                for field_path in fields:
                    # 移除.value后缀，因为model_dump(mode='json')已经处理了枚举值
                    clean_field_path = field_path.replace('.value', '')

                    if isinstance(item_dict, dict):
                        value = cls._get_nested_dict_value(
                            item_dict, clean_field_path)
                    else:
                        value = cls._get_nested_attr(
                            item_dict, clean_field_path)

                    field_values.append(str(value))

                data_str = ','.join(field_values)
                data_list.append(data_str)

            # 根据消息类型创建WebSocket消息
            if msg_type == WebSocketMsgTypeEnum.SNAPSHOT:
                return WebSocketDataResponseModel(
                    type=msg_type.value,
                    table=get_ws_table_name(table_type),
                    data=data_list,
                    is_snap_last=1
                )
            elif msg_type == WebSocketMsgTypeEnum.DATA:
                return WebSocketDataResponseModel(
                    type=msg_type.value,
                    table=get_ws_table_name(table_type),
                    data=data_list
                )
            else:
                flogger.warning(f"unsupported message type: {msg_type}")
                return None

        return converter_method

    @staticmethod
    def _get_nested_attr(obj: Any, attr_path: str) -> Any:
        """获取嵌套属性值，支持 'status.value' 这样的路径"""
        attrs = attr_path.split('.')
        current = obj
        for attr in attrs:
            current = getattr(current, attr)
        return current

    @staticmethod
    def _get_nested_dict_value(data_dict: Dict[str, Any], key_path: str) -> Any:
        """获取嵌套字典值，支持 'status.value' 这样的路径"""
        keys = key_path.split('.')
        current = data_dict
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current


class DataConverter(metaclass=DataConverterMeta):
    """数据转换器"""

    @classmethod
    def get_converter_method(cls, table_type: table_enum) -> Callable:
        """根据表类型获取对应的转换方法"""
        method_name = f"convert_{table_type.name}"
        return getattr(cls, method_name, None)

    @classmethod
    def convert_by_table_type(cls, table_type: table_enum, strategy_data: List[Any], msg_type: WebSocketMsgTypeEnum) -> WebSocketDataResponseModel:
        """通用转换方法，根据表类型自动选择转换器"""
        converter = cls.get_converter_method(table_type)
        if converter:
            return converter(strategy_data, msg_type)
        else:
            flogger.error(f"no converter found for table type: {table_type}")
            return None

    @classmethod
    def get_supported_tables(cls) -> List[table_enum]:
        """获取支持的表类型列表"""
        return list(cls._field_configs.keys())
