import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict, field, fields
from utils.flog import flogger, flog_init


@dataclass
class StrategyEngineNode:
    """策略引擎节点配置"""
    node_id: int = 1
    host: str = "localhost"
    port: int = 5000
    timeout: int = 30

    @property
    def url(self) -> str:
        return f"http://{self.host}:{self.port}"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class StrategyEngineInfo:
    """策略引擎运行时信息"""
    node_id: int
    config: StrategyEngineNode
    is_ready: bool = False


@dataclass
class MidServerConfig:
    """中台服务配置"""
    host: str = "0.0.0.0"
    ws_port: int = 8765  # WebSocket服务端口
    http_port: int = 8766  # HTTP API服务端口

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class LogConfig:
    """日志配置"""
    log_level: str = "INFO"
    log_file: str = "./log/py_strategy_mid_server.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class AppConfig:
    """应用配置"""
    mid_server: MidServerConfig = field(default_factory=MidServerConfig)
    log: LogConfig = field(default_factory=LogConfig)
    strategy_engines: List[StrategyEngineNode] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        # 将strategy_engines转换为字典列表
        result['strategy_engines'] = [engine.to_dict() for engine in self.strategy_engines]
        return result


class ConfigManager:
    """配置管理器"""

    _instance = None

    @classmethod
    def get_instance(cls, config_file: str = None):
        if cls._instance is None:
            cls._instance = cls(config_file)
        return cls._instance

    def __init__(self, config_file: str = None):
        self.config_file = config_file
        self.config = AppConfig()
        self.load()

    def load(self) -> AppConfig:
        """加载配置"""
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config_dict = json.load(f)
                self._update_config_from_dict(config_dict)
                print(
                    f"Successfully loaded configuration from {self.config_file}")
            except Exception as e:
                print(
                    f"Failed to load configuration from {self.config_file}: {e}")
                print("Using default configuration")
                self.config = AppConfig()
                self.save()
        elif self.config_file:
            print(f"{self.config_file} does not exists")
            self.config = AppConfig()
            self.save()
        else:
            print("No config file, Creating default configuration file")
            self.config = AppConfig()
            self.save()

        # 加载策略引擎配置
        self._load_strategy_engines_config()

        self._configure_logging()
        flogger.info("init struct flogger")
        return self.config

    def _configure_logging(self):
        log_config = self.config.log
        flog_init(log_config.log_file, log_config.log_level)

    def save(self) -> None:
        """保存配置"""
        if not self.config_file:
            print("no config file specified, cannot save")
            return
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

            config_dict = {
                "host": self.config.mid_server.host,
                "ws_port": self.config.mid_server.ws_port,
                "http_port": self.config.mid_server.http_port,
                "log_level": self.config.log.log_level,
                "log_file": self.config.log.log_file
            }

            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            print(f"Configuration saved to {self.config_file}")
        except Exception as e:
            print(f"Failed to save configuration to {self.config_file}: {e}")

    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        # 处理中台服务配置
        mid_server_dict = {}
        for key in ["host", "ws_port", "http_port"]:
            if key in config_dict:
                mid_server_dict[key] = config_dict[key]

        if mid_server_dict:
            updated_mid_server = self._update_dataclass_from_dict(
                MidServerConfig, self.config.mid_server, mid_server_dict)
            setattr(self.config, "mid_server", updated_mid_server)

        # 处理日志配置
        log_dict = {}
        for key in ["log_level", "log_file"]:
            if key in config_dict:
                log_dict[key] = config_dict[key]

        if log_dict:
            updated_log = self._update_dataclass_from_dict(
                LogConfig, self.config.log, log_dict)
            setattr(self.config, "log", updated_log)

    def _load_strategy_engines_config(self):
        """从策略引擎配置文件加载策略引擎节点信息"""
        strategy_engine_config_file = "../config/py_strategy_engine_config.json"

        if not os.path.exists(strategy_engine_config_file):
            print(f"Strategy engine config file not found: {strategy_engine_config_file}")
            return

        try:
            with open(strategy_engine_config_file, "r", encoding="utf-8") as f:
                strategy_config_dict = json.load(f)

            self.config.strategy_engines = []

            # 遍历策略引擎配置，提取节点信息
            for config_key, node_config in strategy_config_dict.items():
                if config_key.startswith("py_strategy_engine "):
                    # 从key中提取node_id
                    try:
                        node_id = int(config_key.split(" ")[1])

                        # 从http_server配置中提取host和port
                        http_server_config = node_config.get("http_server", {})
                        host = http_server_config.get("host", "localhost")
                        port = http_server_config.get("port", 5000)

                        # 创建策略引擎节点
                        engine_node = StrategyEngineNode(
                            node_id=node_id,
                            host=host,
                            port=port
                        )
                        self.config.strategy_engines.append(engine_node)

                    except (ValueError, IndexError) as e:
                        print(f"Failed to parse node_id from config key '{config_key}': {e}")
                        continue

            print(f"Loaded {len(self.config.strategy_engines)} strategy engine nodes from {strategy_engine_config_file}")

        except Exception as e:
            print(f"Failed to load strategy engine config from {strategy_engine_config_file}: {e}")

    def _update_dataclass_from_dict(self, dataclass_type, current_config_instance, update_dict: Dict[str, Any]):
        """从字典更新dataclass实例"""
        update_kwargs = {}
        for field in fields(dataclass_type):
            field_name = field.name
            if field_name in update_dict:
                update_kwargs[field_name] = update_dict[field_name]
            else:
                update_kwargs[field_name] = getattr(current_config_instance, field_name)
        return dataclass_type(**update_kwargs)

    def _create_dataclass_from_dict(self, dataclass_type, data_dict: Dict[str, Any]):
        """从字典创建dataclass实例"""
        kwargs = {}
        for field in fields(dataclass_type):
            field_name = field.name
            if field_name in data_dict:
                kwargs[field_name] = data_dict[field_name]
            # 如果字段不在字典中，使用默认值（dataclass会自动处理）
        return dataclass_type(**kwargs)

    def get_config(self) -> AppConfig:
        """获取配置"""
        return self.config

    def get_mid_server_config(self) -> MidServerConfig:
        """获取中台服务配置"""
        return self.config.mid_server

    def get_strategy_engines(self) -> List[StrategyEngineNode]:
        """获取策略引擎节点列表"""
        return self.config.strategy_engines

    def get_log_config(self) -> LogConfig:
        """获取日志配置"""
        return self.config.log
