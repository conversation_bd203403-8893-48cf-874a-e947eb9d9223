from .enum import *
from typing import List, Optional, Any, Dict
from pydantic import BaseModel


class StrategyInstanceMsg(BaseModel):
    strategy_instance_id: int  # 策略实例id
    strategy_name: str  # 策略名
    strategy_engine_id: int  # 策略引擎id
    strategy_instance_name: str  # 实例名
    user_id: int  # 用户名关联id
    # 策略实例状态，1-初始状态，2-运行中，3-已暂停，4-已删除，5-已失效
    strategy_instance_state: StrategyStateEnum
    trading_account_id: int  # 交易账户id
    strategy_instance_priority: int  # 实例订阅消息接收优先级，分为：最高、高、中、低、最低五档
    last_operator_id: int  # 最近操作人id
    strategy_pause_reason: StrategyPauseReasonEnum  # 策略暂停理由


class StrategyDeployMsg(BaseModel):
    strategy_name: str  # 策略名
    strategy_engine_id: int  # 策略引擎id
    strategy_version: str  # 策略版本
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效
    last_operator_id: int  # 最近操作人id


class StrategyParamMsg(BaseModel):
    strategy_name: str  # 策略名
    param_key: str  # 参数名
    param_type: str  # 参数类型
    param_value: str  # 参数值
    index: int  # 参数序号
    tick: float  # 最小变动单位
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class StrategyEnumMsg(BaseModel):
    strategy_name: str  # 策略名
    enum_name: str  # 枚举名
    enum_value: str  # 枚举名
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class StrategyInstanceParamMsg(BaseModel):
    strategy_instance_id: int  # 策略实例id
    strategy_engine_id: int  # 策略引擎id
    param_key: str  # 参数名
    param_value: str  # 参数值
    last_operator_id: int  # 最近操作人id


class WebSocketDataResponseModel(BaseModel):
    type: str
    table: str
    data: List[Any]
    is_snap_last: int = None

class WebSocketOpResponseModel(BaseModel):
    op: str
    table: str
    code: int
    message: str
    header: str

class ApiResponseModel(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    request_id: Optional[int] = None


class HttpDataItem(BaseModel):
    name: str
    value: Dict[str, Any]


class HttpResponseModel(BaseModel):
    data: List[Optional[HttpDataItem]] = []
    code: int
    message: str


class HeartbeatResponseModel(BaseModel):
    code: int
    message: str


class TablePublisherResponseModel(BaseModel):
    service_type: str = "config"
    tables: List[str] = []
