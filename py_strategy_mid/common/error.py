'''
Created by script, do not edit it!
Date:    2025-03-10 09:13:59
Author:  Autobot
'''
from enum import Enum
from typing import Dict


class ErrorCodeEnum(Enum):
    ERROR_NONE = 0
    ERROR_UNKNOWN = -1
    ERROR_INVALID_ACTION = 4097  # FEBAO:请求方法无效 */
    ERROR_INVALID_PARAM = 4098  # FEBAO:请求参数无效 */
    ERROR_BAD_URL_PARAM = 4099  # FEBAO:请求参数解析失败 */
    ERROR_INVALID_WEBSOCKET_FRAME = 4100  # FEBAO:非法数据帧 */
    ERROR_INVALID_JSON = 4101  # FEBAO:json解析失败 */
    ERROR_INVALID_TABLE = 4102  # FEBAO:订阅表不存在 */
    ERROR_SYSTEM_BUSY = 4103  # FEBAO:业务处理中 */
    ERROR_DUPLICATE_FIELD = 4104  # FEBAO:存在相同主键的重复数据 */
    ERROR_EXECUTE_SQL_FAILED = 4105  # FEBAO:执行SQL失败 */
    ERROR_QUERY_INSERT_RESULT_FAILED = 4106  # FEBAO:查询不到已保存数据 */
    ERROR_FIELD_CONFIG_FAILED = 4107  # FEBAO:数据访问绑定无效 */
    ERROR_NO_SQL_CONNECTION = 4108  # FEBAO:数据访问链接无效 */
    ERROR_ADD_OR_UPDATE_FAILED = 4109  # FEBAO:数据保存失败 */
    ERROR_NO_AUTO_ID = 4110  # FEBAO:获取自增ID失败 */
    ERROR_NO_SPOT_RECORD = 4111  # FEBAO:SPOT数据不存在 */
    ERROR_RESET_SPOT_FAILED = 4112  # FEBAO:SPOT状态重置失败 */
    ERROR_NO_RATE_RECORD = 4113  # FEBAO:rate数据不存在 */
    ERROR_NO_VIRTUAL_PORTFOLIO_RECORD = 4114  # FEBAO:虚拟组合不存在 */
    ERROR_RESET_VIRTUAL_PORTFOLIO_UNDERLYING_FAILED = 4115  # FEBAO:虚拟组合标的重置失败 */
    ERROR_MODIFY_YD_POSITION_FAILED = 4116  # FEBAO:修改昨持仓失败 */
    ERROR_INVALID_ACCOUNT_OR_PASSWORD = 4117  # FEBAO:无效的用户或密码 */
    ERROR_USER_MULTI_LOGIN = 4118  # FEBAO:用户重复登录 */
    ERROR_USER_NOT_LOGIN = 4119  # FEBAO:用户未登录 */
    ERROR_SESSION_TIMEOUT = 4120  # FEBAO:用户会话超时 */
    ERROR_OVERDUE_PASSWORD = 4121  # FEBAO:用户密码失效 */
    ERROR_MULTI_SESSION = 4122  # FEBAO:用户登录冲突 */
    ERROR_INACTIVE_ACCOUNT = 4123  # FEBAO:用户未激活 */
    ERROR_STRATEGY_ENGINE_NOT_FINISH_INITED = 4124  # FEBAO:策略引擎未完成初始化 */
    ERROR_DUPLICATE_STRATEGY_INSTANCE = 4125  # FEBAO:重复的策略实例 */
    ERROR_NO_STRATEGY_INSTANCE = 4126  # FEBAO:策略实例不存在 */
    ERROR_STRATEGY_INSTANCE_ACTION_FAILED = 4127  # FEBAO:策略实例操作失败 */
    ERROR_NOT_MATCH_STRATEGY_INSTANCE = 4128  # FEBAO:交易员与策略实例不匹配 */
    ERROR_FRONT_ASSIGN_TOPIC = 4129  # FEBAO:前置未给请求分配后台路由 */
    ERROR_NOT_MATCH_PORTFOLIO_TRADING_ACCOUNT = 4130  # FEBAO:交易账户所属组合号不匹配 */
    ERROR_INVALID_INSTRUMENT = 4131  # FEBAO:合约非法 */
    ERROR_INVALID_SERIAL = 4132  # FEBAO:系列非法 */
    ERROR_INVALID_HOLIDAY = 4133  # FEBAO:交易日非法 */
    ERROR_REQUEST_TIMEOUT = 4134  # FEBAO:请求超时 */
    ERROR_MODIFY_YD_POSITION_FAILD = 4135  # FEBAO:修改昨持仓失败 */
    ERROR_INVALID_VOLATILITY_FITTING_CONFIG = 4136  # FEBAO:波动率拟合配置错误 */
    ERROR_INVALID_ATM_FORWARD = 4137  # FEBAO:波动率拟合atm_forward错误 */
    ERROR_INVALID_VOLATILITY_FITTING_RANGE = 4138  # FEBAO:波动率拟合范围错误 */
    ERROR_INPUT_PARAM_IS_INVALID = 4139  # FEBAO:波动率插件失败，输入参数无效 */
    ERROR_DATA_NUM_IS_TOO_MUCH = 4140  # FEBAO:波动率插件失败，数据点太多 */
    ERROR_DATA_NUM_IS_TOO_FEW = 4141  # FEBAO:波动率插件失败，数据点太少 */
    ERROR_INPUT_OUTPUT_PARAM_IS_NULL = 4142  # FEBAO:波动率插件失败，输入输出参数为空 */
    ERROR_ATM_FORWARD_IS_INVALID = 4143  # FEBAO:波动率插件失败，atm_forward非法 */
    # FEBAO:波动率插件失败，atm_forward不在拟合范围 */
    ERROR_ATM_FORWARD_IS_NOT_IN_PROPER_RANGE = 4144
    ERROR_FIT_RANGE_IS_INVALID = 4145  # FEBAO:波动率插件失败，fit_range无效 */
    # FEBAO:波动率插件失败，strike与volatility不匹配 */
    ERROR_STRIKE_VOLATILITY_MISMATCH = 4146
    ERROR_VOLATILITY_PLUGIN_UNKNOWN = 4147  # FEBAO:波动率插件失败，未知错误 */
    ERROR_VOLATILITY_FITTING_FAILD = 4148  # FEBAO:波动率拟合计算失败 */
    ERROR_VOLATILITY_AUTO_FIT_EXCEEDED = 4149  # FEBAO:波动率自动拟合超过最大次数 */
    ERROR_VOLATILITY_DEVIATION_FAILED = 4150  # FEBAO:波动率偏离度计算失败 */
    ERROR_STRATEGY_ENGINE_ID = 4151  # FEBAO:错误的引擎号 */
    ERROR_NO_THREAD_START = 4152  # FEBAO:无线程启动 */
    ERROR_NO_STRATEGY_PLUGIN = 4153  # FEBAO:没有该策略插件 */
    ERROR_NO_THREAD = 4154  # FEBAO:无该线程 */
    ERROR_INVALID_PAGE_QUERY_ID = 4155  # FEBAO:找不到查询任务 */
    ERROR_INVALID_EXCHANGE = 4156  # FEBAO:非法交易所 */
    ERROR_INVALID_PRODUCT_LIST = 4157  # FEBAO:产品列表中包含非法的产品号 */
    ERROR_INVALID_TRADING_ACCOUNT = 4158  # FEBAO:交易账户不匹配 */
    ERROR_EXIST_UNTRADED_ORDER = 4159  # FEBAO:存在未成交订单 */
    ERROR_PORTFOLIO_POSITION_NOT_EMPTY = 4160  # FEBAO:组合持仓存在有效数据 */
    ERROR_NO_CURRENT_PRICE = 4161  # FEBAO:当前价不存在 */
    ERROR_PRICE_DEVIATION_RISK_FAILD_NO_BASEPRICE = 4162  # FEBAO:价格偏离度检查失败，找不到指定基准价 */
    # FEBAO:价格偏离度检查失败，价格超过指定阈值 */
    ERROR_PRICE_DEVIATION_RISK_FAILD_OUT_OF_THRESHOLD = 4163
    ERROR_STRATEGY_PLUGIN_IN_USE = 4164  # FEBAO:策略插件正在使用中 */
    ERROR_INVALID_ROLE_RIGHT = 4165  # FEBAO:用户角色没有该操作权限 */
    ERROR_INSTRUMENT_GROUP_CIRCULARLY_CONTAIN = 4166  # FEBAO:合约组互相包含 */
    ERROR_INVALID_SETTLEMENT = 4167  # FEBAO:非法结算记录 */
    ERROR_REQUEST_NOT_ALL_SUCCESS = 4168  # FEBAO:请求处理未全部成功 */
    ERROR_DELETE_CONFIG = 4169  # FEBAO:配置使用中不可删除 */
    ERROR_CANCEL_QUOTE_BY_INSTRUMENT = 4170  # FEBAO:非深交所不支持按合约撤单 */
    ERROR_CANCEL_QUOTE_TYPE_INVALID = 4171  # FEBAO:单边撤方向错误 */
    ERROR_NOT_SUPPORT_CANCEL_ONESIEDE_QUOTE = 4172  # FEBAO:交易所不支持单边撤 */
    ERROR_NO_MID_RISK_AUTHORITY = 4173  # FEBAO:无事中风控权限 */
    ERROR_INVALID_PRODUCT = 4174  # FEBAO:非法产品 */
    ERROR_QRY_CHECK_TRADE_FAILED = 4175  # FEBAO:查询成交核对结果失败 */
    ERROR_SELF_TRADE_CONFIG = 4176  # FEBAO:自成交算法配置不匹配 */
    ERROR_TOP_SPECIFIC_QUOTE_NOT_EXIST = 4177  # FEBAO:指定单不存在 */
    ERROR_TOP_SPECIFIC_QUOTE_STATUS_IS_WAITTING = 4178  # FEBAO:指定单处于待确认 */
    ERROR_API_APPEND_TO_FLOW_FAILED = 4179  # FEBAO:调用柜台错误：写流失败 */
    ERROR_API_EXIST_TIMEOUT_REQUEST = 4180  # FEBAO:调用柜台错误：存在超时请求 */
    ERROR_API_OUT_OF_FLOW_CONTROL = 4181  # FEBAO:调用柜台错误：超出api流控 */
    ERROR_WRONG_API_VERSION_IN_STRATEGY_PLUGIN = 4182  # FEBAO:该策略插件的api版本不匹配 */
    ERROR_CURRENT_USER_INFO_CHANGED = 4183  # FEBAO:当前用户信息被修改 */
    ERROR_SPLIT_FINAL_COMB = 4184  # FEBAO:终态单拆分失败 */
    ERROR_NO_SERIAL_PRCING_CONFIG = 4185  # FEBAO:无有效定价配置 */
    ERROR_CHANGE_STRATEGY_PARAM_TYPE = 4186  # FEBAO:不允许修改策略参数类型 */
    ERROR_CANCEL_DELAY_FOR_FAILED = 4187  # FEBAO:延迟撤单失败 */
    ERROR_SYSTEM_STATUS_ERROR = 4188  # FEBAO:系统配置错误 */
    ERROR_SYSTEM_STATUS_PREPARING = 4189  # FEBAO:系统准备中 */
    ERROR_TRADING_ACCOUNT_STATUS_EMERGENCY = 4190  # FEBAO:交易账户处于紧急制动状态 */
    ERROR_TRADING_ACCOUNT_EMERGENCY_SWITCH_OFF = 4191  # FEBAO:应急处置开关关闭，不允许切换系统模式 */
    ERROR_UNLOAD_STRATEGY = 4192  # FEBAO:策略卸载失败 */
    ERROR_THE_MAC_ADDRESS_NOT_AUTHORIZED = 4193  # FEBAO:您登录mac地址未授权，请联系管理员 */
    ERROR_CANCEL_EXECORDER_FAILED = 4194  # FEBAO:撤销期权行权失败 */
    ERROR_CANCEL_TIMEOUT_EXECORDER = 4195  # FEBAO:超时行权单撤单失败 */
    ERROR_CANCEL_FINAL_EXECORDER = 4196  # FEBAO:终态行权单撤单失败 */
    ERROR_CANCEL_OPTION_AUTOCLOSE_FAILED = 4197  # FEBAO:撤销期权自对冲失败 */
    ERROR_CANCEL_OPTION_HOLD_FAILED = 4198  # FEBAO:撤销期权留仓失败 */
    ERROR_FUTURE_AUTOCLOSE_FLAG = 4199  # FEBAO:错误的期货自对冲 */
    ERROR_INSTRUMENT_TYPE = 4200  # FEBAO:错误的合约类型 */
    ERROR_DUPLICATE_OPTION_AUTOCLOSE = 4201  # FEBAO:期权自对冲重复设置 */
    ERROR_OPTION_EXEC = 4202  # FEBAO:期权行权请求失败 */
    ERROR_SWAP_WITHOUT_OFFSET = 4203  # FEBAO:互换交易未指定开平仓 */
    ERROR_ARBI_CANNOT_QUOTE = 4204  # FEBAO:套利合约不允许报价 */
    ERROR_ARBI_WRONG_PRICE_CATEGORY = 4205  # FEBAO:套利合约价格类型错误 */
    # FEBAO:本方订单薄存在状态为待确认、已确认或部分成交报单或报价请处理后再尝试！ */
    ERROR_RESET_ORDER_BOOK_FAILED = 4206
    ERROR_RISK_FUND_POSITION_COST_LIMIT = 4207  # FEBAO:基金持仓成本限额检查失败 */
    ERROR_RISK_POSITION_CONCENTRATION_RATIO_LIMIT = 4208  # FEBAO:持仓集中度限额检查失败 */
    ERROR_LOAD_SECURITY_INSTRUMENT_GET_FILE_ERROR = 4209  # FEBAO:获取文件失败 */
    ERROR_LOAD_SECURITY_INSTRUMENT_ID_ALREADY_HAVE = 4210  # FEBAO:合约代码已存在 */
    ERROR_LOAD_SECURITY_INSTRUMENT_NO_HAVE_VALUE = 4211  # FEBAO:找不到该合约代码 */
    ERROR_LOAD_SECURITY_INSTRUMENT_SUB_CLASS_ERROR = 4212  # FEBAO:合约和产品不匹配 */
    ERROR_DELETE_SECURITY_INSTRUMENT_HAVE_POSITION = 4213  # FEBAO:删除合约存在持仓 */
    ERROR_RISK_OPEN_TRADE_LIMIT = 4214  # FEBAO:今开仓量限额检查失败 */
    ERROR_RISK_WEEK_SUM_TRADE_LIMIT = 4215  # FEBAO:自然周成交量限额检查失败 */
    ERROR_NO_INVESTOR_ID = 4216  # FEBAO:股指期货报单未填写资金账户 */
    ERROR_SECURITY_TYPE_TRADE_SOURCE_NO_MATCH = 4217  # FEBAO:合约类型交易来源不匹配 */


class ErrorMsg:
    data: Dict[ErrorCodeEnum, str] = {}

    # 根据 ErrorCodeEnum 和 mm_str_errorid.h 中的对应关系填充错误信息
    data[ErrorCodeEnum.ERROR_NONE] = "success"
    data[ErrorCodeEnum.ERROR_UNKNOWN] = "unknown error"
    data[ErrorCodeEnum.ERROR_INVALID_ACTION] = "invalid action"
    data[ErrorCodeEnum.ERROR_INVALID_PARAM] = "invalid param"
    data[ErrorCodeEnum.ERROR_BAD_URL_PARAM] = "bad url param"
    data[ErrorCodeEnum.ERROR_INVALID_WEBSOCKET_FRAME] = "invalid websocket frame"
    data[ErrorCodeEnum.ERROR_INVALID_JSON] = "invalid json param"
    data[ErrorCodeEnum.ERROR_INVALID_TABLE] = "invalid table"
    data[ErrorCodeEnum.ERROR_SYSTEM_BUSY] = "system busy"
    data[ErrorCodeEnum.ERROR_DUPLICATE_FIELD] = "insert duplicate field"
    data[ErrorCodeEnum.ERROR_EXECUTE_SQL_FAILED] = "execute sql failed"
    data[ErrorCodeEnum.ERROR_QUERY_INSERT_RESULT_FAILED] = "query add or insert result failed"
    data[ErrorCodeEnum.ERROR_FIELD_CONFIG_FAILED] = "find field_id failed"
    data[ErrorCodeEnum.ERROR_NO_SQL_CONNECTION] = "no sql connection"
    data[ErrorCodeEnum.ERROR_ADD_OR_UPDATE_FAILED] = "add or update failed"
    data[ErrorCodeEnum.ERROR_NO_AUTO_ID] = "field has no auto id"
    data[ErrorCodeEnum.ERROR_NO_SPOT_RECORD] = "spot record do not exist"
    data[ErrorCodeEnum.ERROR_RESET_SPOT_FAILED] = "reset status failed"
    data[ErrorCodeEnum.ERROR_NO_RATE_RECORD] = "rate record do not exist"
    data[ErrorCodeEnum.ERROR_NO_VIRTUAL_PORTFOLIO_RECORD] = "virtual_portfolio record do not exist"
    data[ErrorCodeEnum.ERROR_RESET_VIRTUAL_PORTFOLIO_UNDERLYING_FAILED] = "reset virtual_portfolio_underlying failed"
    data[ErrorCodeEnum.ERROR_MODIFY_YD_POSITION_FAILED] = "modify yd_position failed"
    data[ErrorCodeEnum.ERROR_INVALID_ACCOUNT_OR_PASSWORD] = "invalid account or password"
    data[ErrorCodeEnum.ERROR_USER_MULTI_LOGIN] = "user multi login"
    data[ErrorCodeEnum.ERROR_USER_NOT_LOGIN] = "user not login"
    data[ErrorCodeEnum.ERROR_SESSION_TIMEOUT] = "session timout"
    data[ErrorCodeEnum.ERROR_OVERDUE_PASSWORD] = "password overdue"
    data[ErrorCodeEnum.ERROR_MULTI_SESSION] = "multi user login"
    data[ErrorCodeEnum.ERROR_INACTIVE_ACCOUNT] = "inactive account"
    data[ErrorCodeEnum.ERROR_STRATEGY_ENGINE_NOT_FINISH_INITED] = "strategy engine not inited"
    data[ErrorCodeEnum.ERROR_DUPLICATE_STRATEGY_INSTANCE] = "duplicate strategy instance"
    data[ErrorCodeEnum.ERROR_NO_STRATEGY_INSTANCE] = "no strategy instance"
    data[ErrorCodeEnum.ERROR_STRATEGY_INSTANCE_ACTION_FAILED] = "strategy instance action failed"
    data[ErrorCodeEnum.ERROR_NOT_MATCH_STRATEGY_INSTANCE] = "trader not match strategy instance"
    data[ErrorCodeEnum.ERROR_FRONT_ASSIGN_TOPIC] = "front not assign topic for req"
    data[ErrorCodeEnum.ERROR_NOT_MATCH_PORTFOLIO_TRADING_ACCOUNT] = "error portfolio id for trading account"
    data[ErrorCodeEnum.ERROR_INVALID_INSTRUMENT] = "invalid instrument"
    data[ErrorCodeEnum.ERROR_INVALID_SERIAL] = "invalid serial"
    data[ErrorCodeEnum.ERROR_INVALID_HOLIDAY] = "invalid holiday"
    data[ErrorCodeEnum.ERROR_REQUEST_TIMEOUT] = "request timeout"
    data[ErrorCodeEnum.ERROR_MODIFY_YD_POSITION_FAILD] = "modify yd position faild"
    data[ErrorCodeEnum.ERROR_INVALID_VOLATILITY_FITTING_CONFIG] = "invalid volatility fitting config"
    data[ErrorCodeEnum.ERROR_INVALID_ATM_FORWARD] = "invalid volatility fitting atm_forward"
    data[ErrorCodeEnum.ERROR_INVALID_VOLATILITY_FITTING_RANGE] = "invalid volatility fitting range"
    data[ErrorCodeEnum.ERROR_INPUT_PARAM_IS_INVALID] = "volatility plugin invalid param"
    data[ErrorCodeEnum.ERROR_DATA_NUM_IS_TOO_MUCH] = "volatility plugin too much data"
    data[ErrorCodeEnum.ERROR_DATA_NUM_IS_TOO_FEW] = "volatility plugin too few data"
    data[ErrorCodeEnum.ERROR_INPUT_OUTPUT_PARAM_IS_NULL] = "volatility plugin null param"
    data[ErrorCodeEnum.ERROR_ATM_FORWARD_IS_INVALID] = "volatility plugin invalid atm_forward"
    data[ErrorCodeEnum.ERROR_ATM_FORWARD_IS_NOT_IN_PROPER_RANGE] = "volatility plugin atm_forward not in range"
    data[ErrorCodeEnum.ERROR_FIT_RANGE_IS_INVALID] = "volatility plugin invalid range"
    data[ErrorCodeEnum.ERROR_STRIKE_VOLATILITY_MISMATCH] = "volatility plugin strike mismatch"
    data[ErrorCodeEnum.ERROR_VOLATILITY_PLUGIN_UNKNOWN] = "volatility plugin unkown error"
    data[ErrorCodeEnum.ERROR_VOLATILITY_FITTING_FAILD] = "volatility fitting failed"
    data[ErrorCodeEnum.ERROR_VOLATILITY_AUTO_FIT_EXCEEDED] = "volatility auto fit exceeded"
    data[ErrorCodeEnum.ERROR_VOLATILITY_DEVIATION_FAILED] = "volatility deviation failed"
    data[ErrorCodeEnum.ERROR_STRATEGY_ENGINE_ID] = "no strategy engine id"
    data[ErrorCodeEnum.ERROR_NO_THREAD_START] = "no thread started"
    data[ErrorCodeEnum.ERROR_NO_STRATEGY_PLUGIN] = "no strategy plugin"
    data[ErrorCodeEnum.ERROR_NO_THREAD] = "thread id invalid"
    data[ErrorCodeEnum.ERROR_INVALID_PAGE_QUERY_ID] = "invalid page query id"
    data[ErrorCodeEnum.ERROR_INVALID_EXCHANGE] = "invalid exchange"
    data[ErrorCodeEnum.ERROR_INVALID_PRODUCT_LIST] = "invalid product list"
    data[ErrorCodeEnum.ERROR_INVALID_TRADING_ACCOUNT] = "invalid trading account"
    data[ErrorCodeEnum.ERROR_EXIST_UNTRADED_ORDER] = "exist untraded order"
    data[ErrorCodeEnum.ERROR_PORTFOLIO_POSITION_NOT_EMPTY] = "portfolio position is not empty"
    data[ErrorCodeEnum.ERROR_NO_CURRENT_PRICE] = "no current price"
    data[ErrorCodeEnum.ERROR_PRICE_DEVIATION_RISK_FAILD_NO_BASEPRICE] = "price deviation risk failed#no baseprice"
    data[ErrorCodeEnum.ERROR_PRICE_DEVIATION_RISK_FAILD_OUT_OF_THRESHOLD] = "price deviation risk failed#out of threshold"
    data[ErrorCodeEnum.ERROR_STRATEGY_PLUGIN_IN_USE] = "strategy plugin is in use"
    data[ErrorCodeEnum.ERROR_INVALID_ROLE_RIGHT] = "role has no this action right"
    data[ErrorCodeEnum.ERROR_INSTRUMENT_GROUP_CIRCULARLY_CONTAIN] = "instrument group_id has contain circularly"
    data[ErrorCodeEnum.ERROR_INVALID_SETTLEMENT] = "invalid settlement"
    data[ErrorCodeEnum.ERROR_REQUEST_NOT_ALL_SUCCESS] = "request not all success"
    data[ErrorCodeEnum.ERROR_DELETE_CONFIG] = "config is used for another"
    data[ErrorCodeEnum.ERROR_CANCEL_QUOTE_BY_INSTRUMENT] = "exchange is not support by instrument"
    data[ErrorCodeEnum.ERROR_CANCEL_QUOTE_TYPE_INVALID] = "cancel quote type is wrong"
    data[ErrorCodeEnum.ERROR_NOT_SUPPORT_CANCEL_ONESIEDE_QUOTE] = "exchange is not support cancel oneside quote"
    data[ErrorCodeEnum.ERROR_NO_MID_RISK_AUTHORITY] = "no mid risk authority"
    data[ErrorCodeEnum.ERROR_INVALID_PRODUCT] = "invalid product"
    data[ErrorCodeEnum.ERROR_QRY_CHECK_TRADE_FAILED] = "qry trade check result failed"
    data[ErrorCodeEnum.ERROR_SELF_TRADE_CONFIG] = "self trade algorithm config is wrong"
    data[ErrorCodeEnum.ERROR_TOP_SPECIFIC_QUOTE_NOT_EXIST] = "specific quote is not exist"
    data[ErrorCodeEnum.ERROR_TOP_SPECIFIC_QUOTE_STATUS_IS_WAITTING] = "specific quote is waiting"
    data[ErrorCodeEnum.ERROR_API_APPEND_TO_FLOW_FAILED] = "call api failed: append to flow failed"
    data[ErrorCodeEnum.ERROR_API_EXIST_TIMEOUT_REQUEST] = "call api failed: exist timeout request"
    data[ErrorCodeEnum.ERROR_API_OUT_OF_FLOW_CONTROL] = "call api failed: out of flow control"
    data[ErrorCodeEnum.ERROR_WRONG_API_VERSION_IN_STRATEGY_PLUGIN] = "wrong api version in strategy plugin"
    data[ErrorCodeEnum.ERROR_CURRENT_USER_INFO_CHANGED] = "current user info changed"
    data[ErrorCodeEnum.ERROR_SPLIT_FINAL_COMB] = "split final comb failed"
    data[ErrorCodeEnum.ERROR_NO_SERIAL_PRCING_CONFIG] = "no serial pricing config"
    data[ErrorCodeEnum.ERROR_CHANGE_STRATEGY_PARAM_TYPE] = "can not change strategy param type"
    data[ErrorCodeEnum.ERROR_CANCEL_DELAY_FOR_FAILED] = "cancel delay for waiting failed"
    data[ErrorCodeEnum.ERROR_SYSTEM_STATUS_ERROR] = "system status error"
    data[ErrorCodeEnum.ERROR_SYSTEM_STATUS_PREPARING] = "system status preparing"
    data[ErrorCodeEnum.ERROR_TRADING_ACCOUNT_STATUS_EMERGENCY] = "trading account status emergency"
    data[ErrorCodeEnum.ERROR_TRADING_ACCOUNT_EMERGENCY_SWITCH_OFF] = "trading account emergency switch off"
    data[ErrorCodeEnum.ERROR_UNLOAD_STRATEGY] = "unload strategy failed"
    data[ErrorCodeEnum.ERROR_THE_MAC_ADDRESS_NOT_AUTHORIZED] = "the mac address not authorized"
    data[ErrorCodeEnum.ERROR_CANCEL_EXECORDER_FAILED] = "cancel Execorder failed"
    data[ErrorCodeEnum.ERROR_CANCEL_TIMEOUT_EXECORDER] = "cancel timeout Execorder failed"
    data[ErrorCodeEnum.ERROR_CANCEL_FINAL_EXECORDER] = "cancel final Execorder failed"
    data[ErrorCodeEnum.ERROR_CANCEL_OPTION_AUTOCLOSE_FAILED] = "cancel option_autoclose failed"
    data[ErrorCodeEnum.ERROR_CANCEL_OPTION_HOLD_FAILED] = "cancel option hold error"
    data[ErrorCodeEnum.ERROR_FUTURE_AUTOCLOSE_FLAG] = " error future autoclose flag"
    data[ErrorCodeEnum.ERROR_INSTRUMENT_TYPE] = " error instrument type"
    data[ErrorCodeEnum.ERROR_DUPLICATE_OPTION_AUTOCLOSE] = "duplicate option_autoclose"
    data[ErrorCodeEnum.ERROR_OPTION_EXEC] = "error Execorder "
    data[ErrorCodeEnum.ERROR_SWAP_WITHOUT_OFFSET] = "swap order without offset flag"
    data[ErrorCodeEnum.ERROR_ARBI_CANNOT_QUOTE] = "arbitrage instrument can not quote"
    data[ErrorCodeEnum.ERROR_ARBI_WRONG_PRICE_CATEGORY] = "arbitrage instrument wrong price category"
    data[ErrorCodeEnum.ERROR_RESET_ORDER_BOOK_FAILED] = "reset order book failed"
    data[ErrorCodeEnum.ERROR_RISK_FUND_POSITION_COST_LIMIT] = "out of position cost limit"
    data[ErrorCodeEnum.ERROR_RISK_POSITION_CONCENTRATION_RATIO_LIMIT] = "out of position concentration ratio limit"
    data[ErrorCodeEnum.ERROR_LOAD_SECURITY_INSTRUMENT_GET_FILE_ERROR] = "load security instrument cannot get file"
    data[ErrorCodeEnum.ERROR_LOAD_SECURITY_INSTRUMENT_ID_ALREADY_HAVE] = "load security instrument already have"
    data[ErrorCodeEnum.ERROR_LOAD_SECURITY_INSTRUMENT_NO_HAVE_VALUE] = "load security instrument no have value"
    data[ErrorCodeEnum.ERROR_LOAD_SECURITY_INSTRUMENT_SUB_CLASS_ERROR] = "load security instrument sub class error"
    data[ErrorCodeEnum.ERROR_DELETE_SECURITY_INSTRUMENT_HAVE_POSITION] = "delete security instrument have position"
    data[ErrorCodeEnum.ERROR_RISK_OPEN_TRADE_LIMIT] = "out of open trade limit"
    data[ErrorCodeEnum.ERROR_RISK_WEEK_SUM_TRADE_LIMIT] = "out of week sum trade limit"
    data[ErrorCodeEnum.ERROR_NO_INVESTOR_ID] = "cffex future no investor id"
    data[ErrorCodeEnum.ERROR_SECURITY_TYPE_TRADE_SOURCE_NO_MATCH] = "security type trade source no match"

    @classmethod
    def get_error_msg(cls, target: ErrorCodeEnum) -> str:
        return cls.data.get(target, "unknown error")
