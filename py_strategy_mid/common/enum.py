from enum import Enum


class IntStatusEnum(Enum):
    INACTIVATE = '0'
    ACTIVATE = '1'


class StrategyStateEnum(Enum):
    STRATEGY_INIT_STAT = '1'  # 初始状态
    STRATEGY_RUNNING_STAT = '2'  # 运行中
    STRATEGY_PAUSE_STAT = '3'  # 已暂停
    STRATEGY_DELETE_STAT = '4'  # 已删除
    STRATEGY_TIMEOUT_STAT = '5'  # 已失效


class StrategyPauseReasonEnum(Enum):
    PAUSE_BY_UNKNOWN = '0'  # 未知
    PAUSE_BY_USER_OPERATOR = '1'  # 用户手动暂停
    PAUSE_BY_EMERGENCY = '2'  # 紧急制动暂停
    PAUSE_BY_STRATEGY_OPERATOR = '3'  # 策略主动暂停
    PAUSE_BY_TIMEOUT = '4'  # 中后台连接超时暂停


class WebSocketMsgTypeEnum(Enum):
    SUBSCRIBE = 'sub'
    UNSUBSCRIBE = 'unsub'
    SNAPSHOT = 'snap'
    DATA = 'data'
