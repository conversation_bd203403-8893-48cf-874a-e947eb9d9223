from enum import Enum
from typing import Type, Dict
from pydantic import BaseModel


class table_enum(Enum):
    """表格枚举"""
    py_strategy_enum_field = 0X0001  # 策略枚举信息维护
    py_strategy_param_field = 0X0002  # 策略参数信息维护
    py_strategy_deploy_field = 0X0003  # 策略部署信息维护
    py_strategy_instance_field = 0X0004  # 策略实例信息维护
    py_strategy_instance_param_field = 0X0005  # 策略实例参数信息维护


# 表格名称字典（保持向后兼容）
table_name_dict: dict[str, table_enum] = {}
table_name_dict['py_strategy_enum_field'] = table_enum.py_strategy_enum_field
table_name_dict['py_strategy_param_field'] = table_enum.py_strategy_param_field
table_name_dict['py_strategy_deploy_field'] = table_enum.py_strategy_deploy_field
table_name_dict['py_strategy_instance_field'] = table_enum.py_strategy_instance_field
table_name_dict['py_strategy_instance_param_field'] = table_enum.py_strategy_instance_param_field

# WebSocket 表名映射（现在直接用枚举名）
ws_table_name_dict = {
    table_enum.py_strategy_enum_field: table_enum.py_strategy_enum_field.name,
    table_enum.py_strategy_param_field: table_enum.py_strategy_param_field.name,
    table_enum.py_strategy_deploy_field: table_enum.py_strategy_deploy_field.name,
    table_enum.py_strategy_instance_field: table_enum.py_strategy_instance_field.name,
    table_enum.py_strategy_instance_param_field: table_enum.py_strategy_instance_param_field.name
}


def get_table_enum(key: str) -> table_enum:
    """根据表格名称获取表格枚举"""
    return table_name_dict.get(key)


def get_ws_table_name(table_id: table_enum) -> str:
    """根据表格枚举获取WebSocket表名（现在直接返回枚举名）"""
    return table_id.name


# 表格枚举到模型类的映射
table_to_model_class: Dict[table_enum, Type[BaseModel]] = {}


def get_table_model_class(table_id: table_enum) -> Type[BaseModel]:
    """根据表格枚举获取对应的模型类"""
    return table_to_model_class.get(table_id)


# 延迟导入并注册映射，避免循环导入
def _init_table_model_mappings():
    """初始化表格到模型的映射"""
    try:
        # 延迟导入models模块
        from .models import (
            StrategyEnumMsg, StrategyParamMsg, StrategyDeployMsg,
            StrategyInstanceMsg, StrategyInstanceParamMsg
        )

        # 注册映射关系
        table_to_model_class[table_enum.py_strategy_enum_field] = StrategyEnumMsg
        table_to_model_class[table_enum.py_strategy_param_field] = StrategyParamMsg
        table_to_model_class[table_enum.py_strategy_deploy_field] = StrategyDeployMsg
        table_to_model_class[table_enum.py_strategy_instance_field] = StrategyInstanceMsg
        table_to_model_class[table_enum.py_strategy_instance_param_field] = StrategyInstanceParamMsg

    except ImportError as e:
        # 如果导入失败，记录警告但不抛出异常
        pass


# 初始化映射
_init_table_model_mappings()
