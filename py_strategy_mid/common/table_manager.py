from functools import wraps
from common.mdb import MemoryDatabase
from common.models import *
from utils.flog import flogger
from common.table_enum import table_enum


def query_fields(*fields):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        wrapper.query_fields = list(fields)
        return wrapper
    return decorator


def init_tables(mdb: MemoryDatabase) -> None:

    flogger.info("init mdb tables")

    # 定义主键函数
    @query_fields('strategy_instance_id')
    def strategy_instance_pk(data: StrategyInstanceMsg) -> str:  # 策略实例表主键
        return str(data.strategy_instance_id)

    @query_fields('strategy_name', 'param_key')
    def strategy_param_pk(data: StrategyParamMsg) -> str:  # 策略参数表主键
        return f"{data.strategy_name}_{data.param_key}"

    @query_fields('strategy_name', 'enum_name')
    def strategy_enum_pk(data: StrategyEnumMsg) -> str:  # 策略枚举表主键
        return f"{data.strategy_name}_{data.enum_name}"

    @query_fields('strategy_name', 'strategy_engine_id')
    def strategy_deploy_pk(data: StrategyDeployMsg) -> str:  # 策略表主键
        return f"{data.strategy_name}_{data.strategy_engine_id}"

    @query_fields('strategy_instance_id', 'param_key')
    def strategy_instance_param_pk(data: StrategyInstanceParamMsg) -> str:  # 策略实例参数表主键
        return f"{data.strategy_instance_id}_{data.param_key}"

    mdb.register_table(table_enum.py_strategy_instance_field,
                       strategy_instance_pk)  # 策略实例表
    mdb.register_table(table_enum.py_strategy_deploy_field,
                       strategy_deploy_pk)  # 策略部署表
    mdb.register_table(table_enum.py_strategy_param_field,
                       strategy_param_pk)  # 策略参数表
    mdb.register_table(table_enum.py_strategy_enum_field,
                       strategy_enum_pk)  # 策略枚举表
    mdb.register_table(table_enum.py_strategy_instance_param_field,
                       strategy_instance_param_pk)  # 策略实例参数表

    flogger.info("finish init mdb tables")
