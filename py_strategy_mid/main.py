import asyncio
import signal
import sys
import time
from contextlib import asynccontextmanager
from typing import Optional, Any
from fastapi import FastAPI, WebSocket, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from strategy_mid_server.mid_server import StrategyMidServer
from common.mdb import MemoryDatabase
from common.models import *
from utils.flog import flogger
from utils.csv_converter import CSVConverter
from config.config import ConfigManager


class Main:
    """策略中台主应用类"""

    def __init__(self):
        config_file = "../config/py_strategy_mid_config.json"
        self.config = ConfigManager.get_instance(config_file).get_config()
        flogger.info("init py strategy mid server", config=self.config)
        # 组件初始化
        self.mdb = MemoryDatabase()
        self.mid_server: Optional[StrategyMidServer] = None
        self.app: Optional[FastAPI] = None
        self.http_server: Optional[uvicorn.Server] = None
        self.ws_server: Optional[uvicorn.Server] = None
        self.running = False

        # 跟踪后台任务
        self.background_tasks: set = set()

        # 设置应用
        self._setup_app()
        flogger.info("py strategy mid server init complete")

    def _check_mid_server_ready(self) -> Optional[HttpResponseModel]:
        if not self.mid_server:
            return HttpResponseModel(
                data=[],
                code=-1,
                message="mid server not initialized"
            )
        return None

    def _create_background_task(self, coro):
        """创建并跟踪后台任务"""
        task = asyncio.create_task(coro)
        self.background_tasks.add(task)
        task.add_done_callback(self.background_tasks.discard)
        return task

    def _setup_app(self):
        """设置FastAPI应用"""
        self.app = FastAPI(
            title="py_strategy_mid",
            description="manage python strategy",
            version="1.0.0",
            lifespan=self._lifespan
        )

        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self._setup_routes()

    def _setup_routes(self):
        """设置所有路由"""
        @self.app.get("/heartbeat")
        async def heartbeat():
            """心跳检查"""
            if self.mid_server and self.running:
                return HeartbeatResponseModel(code=0, message="success")
            else:
                return HeartbeatResponseModel(code=-1, message="failed")

        @self.app.post("/py_strategy/create_instance", response_model=HttpResponseModel)
        async def create_strategy_instance(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("create strategy instance request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                strategy_instance = CSVConverter.parse_csv_to_model(request_data, StrategyInstanceMsg)
                result = await self.mid_server.handle_create_strategy_instance(strategy_instance)
                if result.code == 0:
                    self._create_background_task(
                        self.mid_server.publish_strategy_instance(strategy_instance.strategy_instance_id)
                    )
                return result
            except Exception as e:
                flogger.error("create strategy instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"create instance failed: {str(e)}"
                )

        @self.app.post("/py_strategy/update_instance_param", response_model=HttpResponseModel)
        async def update_instance_param(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("update instance param request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                params: List[StrategyInstanceParamMsg] = []
                lines = request_data.strip().split('\n')
                if not lines:
                    return HttpResponseModel(
                        data=[],
                        code=-1,
                        message="empty message"
                    )
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        param = CSVConverter.parse_csv_to_model(line, StrategyInstanceParamMsg)
                        params.append(param)
                    except Exception as e:
                        flogger.error("parse csv line failed",
                                     line_num=line_num,
                                     line_data=line,
                                     error=str(e))
                        return HttpResponseModel(
                            data=[],
                            code=-1,
                            message=f"parse csv line {line_num} failed: {str(e)}"
                        )
                result = await self.mid_server.handle_update_instance_param(params)
                if result.code == 0:
                    self._create_background_task(
                        self.mid_server.publish_strategy_instance_param(params[0].strategy_instance_id)
                    )
                return result
            except Exception as e:
                flogger.error("update instance param failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"update instance param failed: {str(e)}"
                )

        @self.app.post("/py_strategy/modify_instance_priority", response_model=HttpResponseModel)
        async def modify_instance_priority(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("modify instance priority request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                strategy_instance = CSVConverter.parse_csv_to_model(request_data, StrategyInstanceMsg)
                result = await self.mid_server.handle_modify_instance_priority(strategy_instance)
                if result.code == 0:
                    self._create_background_task(
                        self.mid_server.publish_strategy_instance(strategy_instance.strategy_instance_id)
                    )
                return result
            except Exception as e:
                flogger.error("modify instance priority failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"modify instance priority failed: {str(e)}"
                )

        @self.app.get("/py_strategy/delete_instance", response_model=HttpResponseModel)
        async def delete_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("delete instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_delete_strategy_instance(trading_account_id, instance_ids)
                if result.code == 0:
                    self._create_background_task(
                        self.mid_server.publish_strategy_instance(instance_ids)
                    )
                return result
            except Exception as e:
                flogger.error("delete instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"delete instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/start_instance", response_model=HttpResponseModel)
        async def start_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("start instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_start_strategy_instance(trading_account_id, instance_ids)
                if result.code == 0:
                    self._create_background_task(
                        self.mid_server.publish_strategy_instance(instance_ids)
                    )
                return result
            except Exception as e:
                flogger.error("start instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"start instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/pause_instance", response_model=HttpResponseModel)
        async def pause_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("pause instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response

                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_pause_strategy_instance(trading_account_id, instance_ids)

                if result.code == 0:
                    self._create_background_task(
                        self.mid_server.publish_strategy_instance(instance_ids)
                    )
                return result
            except Exception as e:
                flogger.error("pause instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"pause instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/req_tablepublisher")
        async def req_tablepublisher():
            """获取表发布信息"""
            try:
                if not self.mid_server:
                    default_response = TablePublisherResponseModel()
                    return [default_response.model_dump()]
                table_publisher_info = self.mid_server.get_table_publisher_info()
                return [table_publisher_info.model_dump()]
            except Exception as e:
                flogger.error("get table publisher info failed", error=str(e))
                default_response = TablePublisherResponseModel()
                return [default_response.model_dump()]

        # WebSocket路由
        @self.app.websocket("/")
        async def websocket_endpoint(websocket: WebSocket):
            if self.mid_server:
                await self.mid_server.get_websocket_server().websocket_endpoint(websocket)

    @asynccontextmanager
    async def _lifespan(self, app: FastAPI):
        """应用生命周期管理"""
        try:
            yield
        except asyncio.CancelledError:
            flogger.debug("lifespan context cancelled during shutdown")
        except Exception as e:
            flogger.error("lifespan context error", error=str(e))
        finally:
            # 确保在应用关闭时执行清理
            flogger.info("lifespan cleanup started")
            if self.running or self.mid_server:
                await self.stop()
            flogger.info("lifespan cleanup completed")

    async def start(self) -> None:
        """启动策略中台服务"""
        flogger.info("starting py strategy mid server")

        # 启动中台服务
        try:
            self.mid_server = StrategyMidServer(
                mdb=self.mdb,
                strategy_engines=self.config.strategy_engines,
            )
            await self.mid_server.start()
            flogger.info("strategy mid server started")
        except Exception as e:
            flogger.error("strategy mid server start failed", error=str(e))
            raise

        # 启动HTTP服务器
        try:
            http_config = uvicorn.Config(
                self.app,
                host=self.config.mid_server.host,
                port=self.config.mid_server.http_port,
                log_level="info"
            )
            self.http_server = uvicorn.Server(http_config)
            self.http_task = self._create_background_task(self.http_server.serve())
            flogger.info("http server started")
        except Exception as e:
            flogger.error("http server start failed", error=str(e))
            if self.mid_server:
                await self.mid_server.stop()
            raise

        # 启动WebSocket服务器
        try:
            ws_config = uvicorn.Config(
                self.app,
                host=self.config.mid_server.host,
                port=self.config.mid_server.ws_port,
                log_level="info"
            )
            self.ws_server = uvicorn.Server(ws_config)
            self.ws_task = self._create_background_task(self.ws_server.serve())
            flogger.info("websocket server started")
        except Exception as e:
            flogger.error("websocket server start failed", error=str(e))
            if self.http_server:
                self.http_server.should_exit = True
            if self.mid_server:
                await self.mid_server.stop()
            raise

        self.running = True
        flogger.info("py strategy mid server started success", active_status=self.running)

    async def stop(self) -> None:
        """停止策略中台服务"""
        flogger.info("stopping py strategy mid server")

        # 取消所有后台任务
        try:
            if self.background_tasks:
                flogger.info("cancelling background tasks", count=len(self.background_tasks))
                for task in self.background_tasks.copy():
                    if not task.done():
                        task.cancel()

                # 等待所有任务完成或被取消
                if self.background_tasks:
                    await asyncio.gather(*self.background_tasks, return_exceptions=True)
                flogger.info("background tasks cancelled")
        except Exception as e:
            flogger.error("background task cancellation failed", error=str(e))

        # 停止HTTP服务器
        try:
            if self.http_server:
                self.http_server.should_exit = True
                flogger.info("http server stopped")
        except Exception as e:
            flogger.error("http server stop failed", error=str(e))

        # 停止WebSocket服务器
        try:
            if self.ws_server:
                self.ws_server.should_exit = True
                flogger.info("websocket server stopped")
        except Exception as e:
            flogger.error("websocket server stop failed", error=str(e))

        # 服务器任务现在已经包含在 background_tasks 中，会被统一处理
        flogger.info("server tasks will be cancelled with background tasks")

        # 停止中台服务
        try:
            if self.mid_server:
                await asyncio.wait_for(self.mid_server.stop(), timeout=10.0)
                flogger.info("strategy mid server stopped")
        except asyncio.TimeoutError:
            flogger.warning("strategy mid server stop timeout, forcing shutdown")
        except Exception as e:
            flogger.error("strategy mid server stop failed", error=str(e))

        self.running = False
        flogger.info("py strategy mid server stopped success", active_status=self.running)


# 全局变量用于信号处理
main_app = None
shutdown_requested = False


def signal_handler(signum: int, _frame: Any) -> None:
    """传统信号处理器"""
    global shutdown_requested
    signal_name = "SIGINT" if signum == signal.SIGINT else "SIGTERM" if signum == signal.SIGTERM else f"SIG{signum}"
    flogger.info("signal received", signal=signal_name, signum=signum)

    shutdown_requested = True
    if main_app and main_app.running:
        main_app.running = False

    flogger.info("shutdown requested via signal")


def main():
    global main_app

    # 注册传统信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    flogger.info("signal handlers registered")

    # 创建主程序
    main_app = Main()

    async def run_async():
        try:
            # 启动服务
            await main_app.start()
            flogger.info("service started, waiting for shutdown signal")

            # 主循环 - 检查关闭标志
            while main_app.running and not shutdown_requested:
                await asyncio.sleep(0.1)  # 短暂休眠，减少CPU使用

                # 定期检查状态
                if shutdown_requested:
                    flogger.info("shutdown signal detected")
                    break

        except KeyboardInterrupt:
            flogger.info("keyboard interrupt received in async context")
        except Exception as e:
            flogger.error("main execution error", error=str(e))
            raise
        finally:
            # 确保停止服务
            flogger.info("shutting down services")
            if main_app.running or main_app.mid_server:
                await main_app.stop()

    # 运行异步逻辑
    try:
        asyncio.run(run_async())
    except KeyboardInterrupt:
        flogger.info("keyboard interrupt received in main")
    except Exception as e:
        flogger.error("main execution error", error=str(e))
        sys.exit(1)

    flogger.info("main function completed")


if __name__ == "__main__":
    main()
