
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _winapi - imported by encodings (delayed, conditional, optional), ntpath (optional), subprocess (optional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), mimetypes (optional)
missing module named msvcrt - imported by subprocess (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), click._winconsole (top-level), click._termui_impl (conditional), getpass (optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), distutils._msvccompiler (top-level), setuptools.msvc (conditional), setuptools._distutils.compilers.C.msvc (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named nt - imported by os (delayed, conditional, optional), ntpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), ctypes (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by /Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named _typeshed - imported by pydantic_core._pydantic_core (top-level), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional), setuptools._distutils.dist (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named importlib_resources.files - imported by importlib_resources (optional), setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by pydantic.mypy (delayed, conditional), setuptools.compat.py310 (conditional), pydantic.v1.mypy (delayed, conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic.deprecated.copy_internals (delayed, conditional), pydantic._internal._generate_schema (delayed, conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), fastapi.openapi.utils (top-level), common.table_enum (top-level), common.models (top-level), utils.model_field_extractor (top-level), utils.csv_converter (top-level)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), fastapi.openapi.models (optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named toml - imported by pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named dotenv - imported by uvicorn.config (delayed, conditional), pydantic.v1.env_settings (delayed, optional)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named rich - imported by pydantic._internal._core_utils (delayed), structlog.tracebacks (optional), structlog.dev (optional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named greenlet - imported by structlog._greenlets (top-level)
missing module named 'rich.traceback' - imported by structlog.dev (optional)
missing module named 'rich.console' - imported by structlog.dev (optional), httpx._main (top-level)
missing module named better_exceptions - imported by structlog.dev (optional)
missing module named colorama - imported by click._compat (delayed, conditional), structlog.dev (optional)
missing module named 'rich.pretty' - imported by structlog.tracebacks (optional)
missing module named zope - imported by structlog.twisted (top-level)
missing module named 'twisted.python' - imported by structlog.twisted (top-level)
missing module named twisted - imported by structlog.twisted (top-level)
missing module named trio - imported by httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named apport_python_hook - imported by exceptiongroup._formatting (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level), anyio._backends._asyncio (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named asyncio.Runner - imported by asyncio (conditional), anyio._backends._asyncio (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level)
missing module named pygments - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named zstandard - imported by httpx._decoders (optional)
missing module named brotlicffi - imported by httpx._decoders (optional)
missing module named brotli - imported by httpx._decoders (optional)
missing module named 'gunicorn.workers' - imported by uvicorn.workers (top-level)
missing module named gunicorn - imported by uvicorn.workers (top-level)
missing module named watchgod - imported by uvicorn.supervisors.watchgodreload (top-level)
missing module named watchfiles - imported by uvicorn.supervisors.watchfilesreload (top-level)
missing module named 'asgiref.typing' - imported by uvicorn.middleware.message_logger (conditional), uvicorn.middleware.proxy_headers (conditional), uvicorn.middleware.wsgi (conditional), uvicorn.config (conditional), uvicorn.protocols.http.flow_control (conditional), uvicorn.protocols.utils (conditional), uvicorn.protocols.http.h11_impl (conditional), uvicorn.protocols.http.httptools_impl (conditional), uvicorn.protocols.websockets.websockets_impl (conditional), uvicorn.protocols.websockets.wsproto_impl (conditional), uvicorn.main (conditional), uvicorn.lifespan.on (conditional)
missing module named 'wsproto.utilities' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named 'wsproto.extensions' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named 'wsproto.connection' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named wsproto - imported by uvicorn.protocols.websockets.wsproto_impl (top-level), uvicorn.protocols.websockets.auto (optional)
missing module named asyncio.timeout_at - imported by asyncio (conditional), websockets.asyncio.compatibility (conditional)
missing module named asyncio.timeout - imported by asyncio (conditional), websockets.asyncio.compatibility (conditional)
missing module named httptools - imported by uvicorn.protocols.http.httptools_impl (top-level), uvicorn.protocols.http.auto (optional)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named asgiref - imported by uvicorn.middleware.asgi2 (conditional)
missing module named yaml - imported by uvicorn.config (delayed, conditional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named 'multipart.multipart' - imported by starlette.formparsers (conditional, optional), starlette.requests (conditional, optional), fastapi.dependencies.utils (delayed, optional)
missing module named 'python_multipart.multipart' - imported by starlette.formparsers (conditional, optional), starlette.requests (conditional, optional)
missing module named multipart - imported by starlette.formparsers (conditional, optional), fastapi.dependencies.utils (delayed, optional)
missing module named python_multipart - imported by starlette.formparsers (conditional, optional), fastapi.dependencies.utils (delayed, optional)
missing module named orjson - imported by fastapi.responses (optional)
missing module named ujson - imported by fastapi.responses (optional)
missing module named _overlapped - imported by asyncio.windows_events (top-level)
