(['/Users/<USER>/Desktop/py_strategy/py_strategy_mid/main.py'],
 ['/Users/<USER>/Desktop/py_strategy/py_strategy_mid'],
 [],
 [('/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.10.12 (main, Jul 20 2025, 17:28:30) [Clang 17.0.0 (clang-1700.0.13.5)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/main.py',
   'PYSOURCE')],
 [('subprocess',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/subprocess.py',
   'PYMODULE'),
  ('selectors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/selectors.py',
   'PYMODULE'),
  ('threading',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_threading_local.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xmlrpc/__init__.py',
   'PYMODULE'),
  ('gzip',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/gzip.py',
   'PYMODULE'),
  ('argparse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/textwrap.py',
   'PYMODULE'),
  ('copy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/copy.py',
   'PYMODULE'),
  ('gettext',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/gettext.py',
   'PYMODULE'),
  ('_compression',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/urllib/request.py',
   'PYMODULE'),
  ('fnmatch',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/fnmatch.py',
   'PYMODULE'),
  ('getpass',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/netrc.py',
   'PYMODULE'),
  ('shlex',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/shlex.py',
   'PYMODULE'),
  ('mimetypes',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/mimetypes.py',
   'PYMODULE'),
  ('getopt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/getopt.py',
   'PYMODULE'),
  ('email.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/utils.py',
   'PYMODULE'),
  ('email.charset',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/encoders.py',
   'PYMODULE'),
  ('quopri',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/quopri.py',
   'PYMODULE'),
  ('email.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/calendar.py',
   'PYMODULE'),
  ('random',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/random.py',
   'PYMODULE'),
  ('statistics',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/statistics.py',
   'PYMODULE'),
  ('fractions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/numbers.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/http/__init__.py',
   'PYMODULE'),
  ('http.cookies',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/http/cookies.py',
   'PYMODULE'),
  ('urllib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/urllib/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/urllib/error.py',
   'PYMODULE'),
  ('string',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/string.py',
   'PYMODULE'),
  ('hashlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/hashlib.py',
   'PYMODULE'),
  ('email',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/_encoded_words.py',
   'PYMODULE'),
  ('uu',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/uu.py',
   'PYMODULE'),
  ('optparse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/email/header.py',
   'PYMODULE'),
  ('bisect',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/bisect.py',
   'PYMODULE'),
  ('xml.sax',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/urllib/parse.py',
   'PYMODULE'),
  ('http.client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/http/client.py',
   'PYMODULE'),
  ('decimal',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/contextvars.py',
   'PYMODULE'),
  ('datetime',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/datetime.py',
   'PYMODULE'),
  ('_strptime',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_strptime.py',
   'PYMODULE'),
  ('base64',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/base64.py',
   'PYMODULE'),
  ('hmac',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/hmac.py',
   'PYMODULE'),
  ('struct',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/struct.py',
   'PYMODULE'),
  ('socket',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/socket.py',
   'PYMODULE'),
  ('tempfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/tempfile.py',
   'PYMODULE'),
  ('shutil',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._common',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/_common.py',
   'PYMODULE'),
  ('importlib._adapters',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/_adapters.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/abc.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/metadata/_text.py',
   'PYMODULE'),
  ('csv',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/csv.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/readers.py',
   'PYMODULE'),
  ('tokenize',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/token.py',
   'PYMODULE'),
  ('pathlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pathlib.py',
   'PYMODULE'),
  ('importlib.util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/tarfile.py',
   'PYMODULE'),
  ('lzma',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/bz2.py',
   'PYMODULE'),
  ('logging',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/dataclasses.py',
   'PYMODULE'),
  ('inspect',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/inspect.py',
   'PYMODULE'),
  ('dis',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/opcode.py',
   'PYMODULE'),
  ('ast',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/multiprocessing/__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/zosccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compilers/C/zos.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compilers/C/unix.py',
   'PYMODULE'),
  ('distutils.util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/util.py',
   'PYMODULE'),
  ('distutils.filelist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/filelist.py',
   'PYMODULE'),
  ('distutils.debug',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/debug.py',
   'PYMODULE'),
  ('distutils.file_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/file_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/dir_util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/text_file.py',
   'PYMODULE'),
  ('sysconfig',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_osx_support',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_osx_support.py',
   'PYMODULE'),
  ('_aix_support',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_bootsubprocess.py',
   'PYMODULE'),
  ('distutils.log',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/log.py',
   'PYMODULE'),
  ('distutils.spawn',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/spawn.py',
   'PYMODULE'),
  ('distutils.dep_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/dep_util.py',
   'PYMODULE'),
  ('distutils.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/errors.py',
   'PYMODULE'),
  ('distutils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/fancy_getopt.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/base_futures.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compilers/C/cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/unittest/util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('platform',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/plistlib.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/version.py',
   'PYMODULE'),
  ('distutils.command',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/command/__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/dist.py',
   'PYMODULE'),
  ('configparser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('glob',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/glob.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/install.py',
   'PYMODULE'),
  ('site',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/webbrowser.py',
   'PYMODULE'),
  ('http.server',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/tty.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/concurrent/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('json',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/json/scanner.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/command/build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/_msvccompiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/ccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/extension.py',
   'PYMODULE'),
  ('distutils.core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/core.py',
   'PYMODULE'),
  ('distutils.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/config.py',
   'PYMODULE'),
  ('cgi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/cgi.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/importlib/resources.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('importlib_resources', '-', 'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('distutils.command.build',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ctypes/wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('__future__',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/__future__.py',
   'PYMODULE'),
  ('stringprep',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_py_abc.py',
   'PYMODULE'),
  ('config.config',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/config/config.py',
   'PYMODULE'),
  ('config',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/config/__init__.py',
   'PYMODULE'),
  ('utils.csv_converter',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/utils/csv_converter.py',
   'PYMODULE'),
  ('utils',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/utils/__init__.py',
   'PYMODULE'),
  ('common.enum',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/enum.py',
   'PYMODULE'),
  ('common', '-', 'PYMODULE'),
  ('pydantic',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/__init__.py',
   'PYMODULE'),
  ('pydantic.validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/version.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/validators.py',
   'PYMODULE'),
  ('uuid',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/uuid.py',
   'PYMODULE'),
  ('ipaddress',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ipaddress.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/utils.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/typing.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/types.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/tools.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/schema.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/parse.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/networks.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/mypy.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/main.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/json.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/generics.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/fields.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/errors.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/decorator.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/config.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/color.py',
   'PYMODULE'),
  ('colorsys',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/colorsys.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/v1/__init__.py',
   'PYMODULE'),
  ('pydantic.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/utils.py',
   'PYMODULE'),
  ('pydantic.typing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/typing.py',
   'PYMODULE'),
  ('pydantic.tools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/tools.py',
   'PYMODULE'),
  ('pydantic.schema',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/schema.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/plugin/_schema_validator.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/plugin/_loader.py',
   'PYMODULE'),
  ('pydantic.plugin',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/plugin/__init__.py',
   'PYMODULE'),
  ('pydantic.parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/parse.py',
   'PYMODULE'),
  ('pydantic.mypy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/mypy.py',
   'PYMODULE'),
  ('pydantic.json',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/json.py',
   'PYMODULE'),
  ('pydantic.generics',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/generics.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/experimental/pipeline.py',
   'PYMODULE'),
  ('annotated_types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/annotated_types/__init__.py',
   'PYMODULE'),
  ('pydantic.experimental',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/experimental/__init__.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/error_wrappers.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/env_settings.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/json.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/__init__.py',
   'PYMODULE'),
  ('pydantic.decorator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/decorator.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/datetime_parse.py',
   'PYMODULE'),
  ('pydantic.color',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/color.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/class_validators.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/alias_generators.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_validators.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_utils.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_signature.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_repr.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_git.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_generics.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_fields.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_config.py',
   'PYMODULE'),
  ('pydantic._internal',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/__init__.py',
   'PYMODULE'),
  ('pydantic.root_model',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/root_model.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/tools.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/config.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/deprecated/class_validators.py',
   'PYMODULE'),
  ('pydantic.warnings',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/warnings.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/type_adapter.py',
   'PYMODULE'),
  ('pydantic.networks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/networks.py',
   'PYMODULE'),
  ('pydantic.main',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/main.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/json_schema.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/functional_validators.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/functional_serializers.py',
   'PYMODULE'),
  ('pydantic.fields',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/fields.py',
   'PYMODULE'),
  ('pydantic.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/errors.py',
   'PYMODULE'),
  ('pydantic.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/config.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.aliases',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/aliases.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_internal/_generate_schema.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/dataclasses.py',
   'PYMODULE'),
  ('pydantic.types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/types.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic_core/core_schema.py',
   'PYMODULE'),
  ('pydantic_core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic_core/__init__.py',
   'PYMODULE'),
  ('pydantic.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/version.py',
   'PYMODULE'),
  ('pydantic._migration',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic/_migration.py',
   'PYMODULE'),
  ('utils.flog',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/utils/flog.py',
   'PYMODULE'),
  ('structlog.processors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/processors.py',
   'PYMODULE'),
  ('structlog.threadlocal',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/threadlocal.py',
   'PYMODULE'),
  ('structlog._greenlets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_greenlets.py',
   'PYMODULE'),
  ('structlog._config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_config.py',
   'PYMODULE'),
  ('structlog.dev',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/dev.py',
   'PYMODULE'),
  ('structlog.contextvars',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/contextvars.py',
   'PYMODULE'),
  ('structlog._output',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_output.py',
   'PYMODULE'),
  ('structlog._native',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_native.py',
   'PYMODULE'),
  ('structlog._base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_base.py',
   'PYMODULE'),
  ('structlog.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/exceptions.py',
   'PYMODULE'),
  ('structlog.tracebacks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/tracebacks.py',
   'PYMODULE'),
  ('structlog._utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_utils.py',
   'PYMODULE'),
  ('structlog._log_levels',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_log_levels.py',
   'PYMODULE'),
  ('structlog._frames',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_frames.py',
   'PYMODULE'),
  ('structlog.typing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/typing.py',
   'PYMODULE'),
  ('structlog',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/__init__.py',
   'PYMODULE'),
  ('structlog.twisted',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/twisted.py',
   'PYMODULE'),
  ('structlog._generic',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/_generic.py',
   'PYMODULE'),
  ('structlog.types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/types.py',
   'PYMODULE'),
  ('structlog.testing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/testing.py',
   'PYMODULE'),
  ('structlog.stdlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog/stdlib.py',
   'PYMODULE'),
  ('common.models',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/models.py',
   'PYMODULE'),
  ('common.mdb',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/mdb.py',
   'PYMODULE'),
  ('common.table_enum',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/table_enum.py',
   'PYMODULE'),
  ('strategy_mid_server.mid_server',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/mid_server.py',
   'PYMODULE'),
  ('strategy_mid_server',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/__init__.py',
   'PYMODULE'),
  ('strategy_mid_server.strategy_instance_service',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/strategy_instance_service.py',
   'PYMODULE'),
  ('httpx',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/__init__.py',
   'PYMODULE'),
  ('httpx._main',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_main.py',
   'PYMODULE'),
  ('httpcore',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/__init__.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/trio.py',
   'PYMODULE'),
  ('httpcore._backends',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/anyio.py',
   'PYMODULE'),
  ('httpcore._utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_utils.py',
   'PYMODULE'),
  ('anyio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/__init__.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_backends/_trio.py',
   'PYMODULE'),
  ('exceptiongroup',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/exceptiongroup/__init__.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/exceptiongroup/_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/exceptiongroup/_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/exceptiongroup/_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/exceptiongroup/_version.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/exceptiongroup/_catch.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/streams/memory.py',
   'PYMODULE'),
  ('anyio.streams',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/streams/__init__.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/lowlevel.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_testing.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_tasks.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_streams.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/_sockets.py',
   'PYMODULE'),
  ('anyio.from_thread',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/from_thread.py',
   'PYMODULE'),
  ('anyio._core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/__init__.py',
   'PYMODULE'),
  ('anyio.abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/abc/__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_backends/_asyncio.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_asyncio_selector_thread.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/events.py',
   'PYMODULE'),
  ('sniffio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/sniffio/__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/sniffio/_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/sniffio/_version.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/protocols.py',
   'PYMODULE'),
  ('anyio._backends',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_backends/__init__.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_typedattr.py',
   'PYMODULE'),
  ('anyio._core._testing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_testing.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_tempfile.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_tasks.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_synchronization.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._streams',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_streams.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_sockets.py',
   'PYMODULE'),
  ('idna',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/streams/tls.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/streams/stapled.py',
   'PYMODULE'),
  ('anyio._core._signals',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_signals.py',
   'PYMODULE'),
  ('anyio._core._resources',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_resources.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_fileio.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_exceptions.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/_core/_eventloop.py',
   'PYMODULE'),
  ('anyio.to_thread',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/anyio/to_thread.py',
   'PYMODULE'),
  ('httpcore._sync',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/__init__.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/socks_proxy.py',
   'PYMODULE'),
  ('httpcore._trace',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_trace.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_synchronization.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/http2.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/http11.py',
   'PYMODULE'),
  ('h11',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/__init__.py',
   'PYMODULE'),
  ('h11._version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_version.py',
   'PYMODULE'),
  ('h11._util',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_util.py',
   'PYMODULE'),
  ('h11._state',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_state.py',
   'PYMODULE'),
  ('h11._events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_events.py',
   'PYMODULE'),
  ('h11._headers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_headers.py',
   'PYMODULE'),
  ('h11._abnf',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_connection.py',
   'PYMODULE'),
  ('h11._writers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_writers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/h11/_readers.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_sync/connection.py',
   'PYMODULE'),
  ('httpcore._ssl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_ssl.py',
   'PYMODULE'),
  ('certifi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/certifi/core.py',
   'PYMODULE'),
  ('httpcore._models',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_models.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_exceptions.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/sync.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/mock.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/base.py',
   'PYMODULE'),
  ('httpcore._async',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/__init__.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_backends/auto.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/http2.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/interfaces.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/http11.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_async/connection.py',
   'PYMODULE'),
  ('httpcore._api',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpcore/_api.py',
   'PYMODULE'),
  ('click',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/__init__.py',
   'PYMODULE'),
  ('click.parser',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/shell_completion.py',
   'PYMODULE'),
  ('click.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/utils.py',
   'PYMODULE'),
  ('click._compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/_termui_impl.py',
   'PYMODULE'),
  ('click.globals',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/globals.py',
   'PYMODULE'),
  ('click.formatting',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/decorators.py',
   'PYMODULE'),
  ('click.core',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/core.py',
   'PYMODULE'),
  ('click.types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click/types.py',
   'PYMODULE'),
  ('httpx._urls',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_utils.py',
   'PYMODULE'),
  ('httpx._urlparse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_urlparse.py',
   'PYMODULE'),
  ('httpx._types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_types.py',
   'PYMODULE'),
  ('httpx._transports',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_transports/__init__.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_transports/wsgi.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_transports/mock.py',
   'PYMODULE'),
  ('httpx._transports.default',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_transports/default.py',
   'PYMODULE'),
  ('httpx._transports.base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_transports/base.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_transports/asgi.py',
   'PYMODULE'),
  ('httpx._status_codes',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_status_codes.py',
   'PYMODULE'),
  ('httpx._models',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_multipart.py',
   'PYMODULE'),
  ('httpx._decoders',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_exceptions.py',
   'PYMODULE'),
  ('httpx._content',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_content.py',
   'PYMODULE'),
  ('httpx._config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_config.py',
   'PYMODULE'),
  ('httpx._client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_client.py',
   'PYMODULE'),
  ('httpx._auth',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_auth.py',
   'PYMODULE'),
  ('httpx._api',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/_api.py',
   'PYMODULE'),
  ('httpx.__version__',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/httpx/__version__.py',
   'PYMODULE'),
  ('utils.rule_checker',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/utils/rule_checker.py',
   'PYMODULE'),
  ('strategy_mid_server.websocket_publisher',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/websocket_publisher.py',
   'PYMODULE'),
  ('utils.data_converter',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/utils/data_converter.py',
   'PYMODULE'),
  ('strategy_mid_server.heartbeat_check_service',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/heartbeat_check_service.py',
   'PYMODULE'),
  ('strategy_mid_server.data_fetch_service',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/data_fetch_service.py',
   'PYMODULE'),
  ('strategy_mid_server.websocket_server',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/websocket_server.py',
   'PYMODULE'),
  ('utils.model_field_extractor',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/utils/model_field_extractor.py',
   'PYMODULE'),
  ('strategy_mid_server.strategy_client',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/strategy_mid_server/strategy_client.py',
   'PYMODULE'),
  ('common.strategy_engine_web_route',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/strategy_engine_web_route.py',
   'PYMODULE'),
  ('common.table_manager',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/table_manager.py',
   'PYMODULE'),
  ('common.error',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/common/error.py',
   'PYMODULE'),
  ('uvicorn',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/__init__.py',
   'PYMODULE'),
  ('uvicorn.workers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/workers.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchgodreload',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/supervisors/watchgodreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/supervisors/watchfilesreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/supervisors/statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/supervisors/multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/supervisors/basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/supervisors/__init__.py',
   'PYMODULE'),
  ('uvicorn.server',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/server.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/websockets/wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/websockets/websockets_impl.py',
   'PYMODULE'),
  ('websockets.typing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/typing.py',
   'PYMODULE'),
  ('websockets.server',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/server.py',
   'PYMODULE'),
  ('websockets.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/utils.py',
   'PYMODULE'),
  ('websockets.protocol',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/protocol.py',
   'PYMODULE'),
  ('websockets.streams',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/streams.py',
   'PYMODULE'),
  ('websockets.frames',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/frames.py',
   'PYMODULE'),
  ('websockets.http11',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/http11.py',
   'PYMODULE'),
  ('websockets.version',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/version.py',
   'PYMODULE'),
  ('websockets.headers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/headers.py',
   'PYMODULE'),
  ('websockets.extensions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/extensions/__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/extensions/base.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/server.py',
   'PYMODULE'),
  ('websockets.legacy',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/__init__.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/protocol.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/framing.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/http.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/handshake.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/async_timeout.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/extensions/permessage_deflate.py',
   'PYMODULE'),
  ('websockets.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/exceptions.py',
   'PYMODULE'),
  ('websockets.datastructures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/datastructures.py',
   'PYMODULE'),
  ('websockets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/__init__.py',
   'PYMODULE'),
  ('websockets.uri',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/uri.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/sync/utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/sync/server.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/sync/messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/sync/connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/sync/client.py',
   'PYMODULE'),
  ('websockets.sync',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/sync/__init__.py',
   'PYMODULE'),
  ('websockets.http',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/http.py',
   'PYMODULE'),
  ('websockets.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/connection.py',
   'PYMODULE'),
  ('websockets.auth',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/auth.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/server.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/messages.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/connection.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/asyncio/client.py',
   'PYMODULE'),
  ('websockets.__main__',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/__main__.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/legacy/auth.py',
   'PYMODULE'),
  ('websockets.client',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/client.py',
   'PYMODULE'),
  ('websockets.imports',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/imports.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/websockets/auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/websockets/__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/http/httptools_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/http/flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/http/auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/http/__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/protocols/__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/middleware/wsgi.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/middleware/message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/middleware/asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/middleware/__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/loops/uvloop.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/loops/auto.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/loops/asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/loops/__init__.py',
   'PYMODULE'),
  ('uvicorn.logging',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/logging.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/lifespan/on.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/lifespan/off.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/lifespan/__init__.py',
   'PYMODULE'),
  ('uvicorn.importer',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/importer.py',
   'PYMODULE'),
  ('uvicorn._types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/_types.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/_subprocess.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/__main__.py',
   'PYMODULE'),
  ('uvicorn.main',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/main.py',
   'PYMODULE'),
  ('uvicorn.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/uvicorn/config.py',
   'PYMODULE'),
  ('logging.config',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/logging/config.py',
   'PYMODULE'),
  ('logging.handlers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/logging/handlers.py',
   'PYMODULE'),
  ('smtplib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/smtplib.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/middleware/cors.py',
   'PYMODULE'),
  ('fastapi.middleware',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/middleware/__init__.py',
   'PYMODULE'),
  ('starlette.middleware',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/middleware/__init__.py',
   'PYMODULE'),
  ('starlette',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/__init__.py',
   'PYMODULE'),
  ('starlette.status',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/status.py',
   'PYMODULE'),
  ('starlette.types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/types.py',
   'PYMODULE'),
  ('starlette.websockets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/websockets.py',
   'PYMODULE'),
  ('starlette.responses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/responses.py',
   'PYMODULE'),
  ('starlette.datastructures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/datastructures.py',
   'PYMODULE'),
  ('starlette.concurrency',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/concurrency.py',
   'PYMODULE'),
  ('starlette.background',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/background.py',
   'PYMODULE'),
  ('starlette._utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/_utils.py',
   'PYMODULE'),
  ('starlette.requests',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/requests.py',
   'PYMODULE'),
  ('starlette.routing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/routing.py',
   'PYMODULE'),
  ('starlette.convertors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/convertors.py',
   'PYMODULE'),
  ('starlette._exception_handler',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/_exception_handler.py',
   'PYMODULE'),
  ('starlette.applications',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/applications.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/middleware/exceptions.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/middleware/errors.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/middleware/base.py',
   'PYMODULE'),
  ('starlette.formparsers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/formparsers.py',
   'PYMODULE'),
  ('starlette.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/exceptions.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/starlette/middleware/cors.py',
   'PYMODULE'),
  ('fastapi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/__init__.py',
   'PYMODULE'),
  ('fastapi.websockets',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/websockets.py',
   'PYMODULE'),
  ('fastapi.responses',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/responses.py',
   'PYMODULE'),
  ('fastapi.requests',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/requests.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/param_functions.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/openapi/models.py',
   'PYMODULE'),
  ('fastapi.openapi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/openapi/__init__.py',
   'PYMODULE'),
  ('fastapi.logger',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/logger.py',
   'PYMODULE'),
  ('fastapi._compat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/_compat.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/openapi/constants.py',
   'PYMODULE'),
  ('fastapi.types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/types.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/exceptions.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/datastructures.py',
   'PYMODULE'),
  ('fastapi.background',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/background.py',
   'PYMODULE'),
  ('fastapi.applications',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/applications.py',
   'PYMODULE'),
  ('fastapi.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/utils.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/openapi/utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/encoders.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/dependencies/utils.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/dependencies/__init__.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/__init__.py',
   'PYMODULE'),
  ('fastapi.security.http',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/http.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/utils.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/api_key.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/oauth2.py',
   'PYMODULE'),
  ('fastapi.security.base',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/security/base.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/concurrency.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/dependencies/models.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/openapi/docs.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/exception_handlers.py',
   'PYMODULE'),
  ('fastapi.routing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/routing.py',
   'PYMODULE'),
  ('fastapi.params',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/fastapi/params.py',
   'PYMODULE'),
  ('typing',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/typing.py',
   'PYMODULE'),
  ('contextlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/contextlib.py',
   'PYMODULE'),
  ('signal',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/signal.py',
   'PYMODULE'),
  ('asyncio',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/asyncio/runners.py',
   'PYMODULE')],
 [('libpython3.10.dylib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/libpython3.10.dylib',
   'BINARY'),
  ('lib-dynload/grp.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/grp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/math.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/select.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/fcntl.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_posixshmem.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_multiprocessing.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/zlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/pyexpat.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_scproxy.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/termios.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/binascii.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_statistics.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha512.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_random.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_ssl.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_hashlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha3.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_blake2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha256.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_md5.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha1.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_bisect.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/unicodedata.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_contextvars.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_decimal.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_datetime.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_struct.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/array.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_socket.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_csv.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/resource.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_lzma.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_bz2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_opcode.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_pickle.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/mmap.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_ctypes.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_queue.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/readline.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/syslog.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_json.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_multibytecodec.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_jp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_kr.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_cn.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_tw.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_hk.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_heapq.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_uuid.cpython-310-darwin.so',
   'EXTENSION'),
  ('pydantic_core/_pydantic_core.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/pydantic_core/_pydantic_core.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_asyncio.cpython-310-darwin.so',
   'EXTENSION'),
  ('websockets/speedups.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets/speedups.cpython-310-darwin.so',
   'EXTENSION'),
  ('libssl.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libssl.3.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libreadline.8.dylib',
   '/opt/homebrew/opt/readline/lib/libreadline.8.dylib',
   'BINARY')],
 [],
 [],
 [('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/certifi/py.typed',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/certifi/cacert.pem',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('websockets-13.0.dist-info/REQUESTED',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/REQUESTED',
   'DATA'),
  ('websockets-13.0.dist-info/LICENSE',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/LICENSE',
   'DATA'),
  ('structlog-25.4.0.dist-info/METADATA',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('structlog-25.4.0.dist-info/licenses/LICENSE-MIT',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/licenses/LICENSE-MIT',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('structlog-25.4.0.dist-info/RECORD',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/RECORD',
   'DATA'),
  ('structlog-25.4.0.dist-info/INSTALLER',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/INSTALLER',
   'DATA'),
  ('websockets-13.0.dist-info/RECORD',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/RECORD',
   'DATA'),
  ('websockets-13.0.dist-info/INSTALLER',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/INSTALLER',
   'DATA'),
  ('websockets-13.0.dist-info/METADATA',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/METADATA',
   'DATA'),
  ('structlog-25.4.0.dist-info/WHEEL',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/WHEEL',
   'DATA'),
  ('websockets-13.0.dist-info/top_level.txt',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/top_level.txt',
   'DATA'),
  ('structlog-25.4.0.dist-info/REQUESTED',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/REQUESTED',
   'DATA'),
  ('structlog-25.4.0.dist-info/licenses/NOTICE',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/licenses/NOTICE',
   'DATA'),
  ('structlog-25.4.0.dist-info/licenses/LICENSE-APACHE',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/structlog-25.4.0.dist-info/licenses/LICENSE-APACHE',
   'DATA'),
  ('websockets-13.0.dist-info/WHEEL',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/site-packages/websockets-13.0.dist-info/WHEEL',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid_fixed/base_library.zip',
   'DATA')],
 [('encodings.zlib_codec',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/encodings/__init__.py',
   'PYMODULE'),
  ('enum',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/enum.py',
   'PYMODULE'),
  ('collections.abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/collections/abc.py',
   'PYMODULE'),
  ('collections',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/collections/__init__.py',
   'PYMODULE'),
  ('posixpath',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/posixpath.py',
   'PYMODULE'),
  ('functools',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/functools.py',
   'PYMODULE'),
  ('reprlib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/reprlib.py',
   'PYMODULE'),
  ('sre_constants',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/sre_constants.py',
   'PYMODULE'),
  ('sre_compile',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/sre_compile.py',
   'PYMODULE'),
  ('copyreg',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/copyreg.py',
   'PYMODULE'),
  ('traceback',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/traceback.py',
   'PYMODULE'),
  ('linecache',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/linecache.py',
   'PYMODULE'),
  ('locale',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/locale.py',
   'PYMODULE'),
  ('weakref',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/weakref.py',
   'PYMODULE'),
  ('os',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/os.py',
   'PYMODULE'),
  ('warnings',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/warnings.py',
   'PYMODULE'),
  ('re',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/re.py',
   'PYMODULE'),
  ('abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/abc.py',
   'PYMODULE'),
  ('ntpath',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/ntpath.py',
   'PYMODULE'),
  ('_collections_abc',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_collections_abc.py',
   'PYMODULE'),
  ('codecs',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/codecs.py',
   'PYMODULE'),
  ('operator',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/operator.py',
   'PYMODULE'),
  ('sre_parse',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/sre_parse.py',
   'PYMODULE'),
  ('genericpath',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/genericpath.py',
   'PYMODULE'),
  ('stat',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/stat.py',
   'PYMODULE'),
  ('keyword',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/keyword.py',
   'PYMODULE'),
  ('heapq',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/heapq.py',
   'PYMODULE'),
  ('types',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/types.py',
   'PYMODULE'),
  ('_weakrefset',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/_weakrefset.py',
   'PYMODULE'),
  ('io',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/io.py',
   'PYMODULE')])
