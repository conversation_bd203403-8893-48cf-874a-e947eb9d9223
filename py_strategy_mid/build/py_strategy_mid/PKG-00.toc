('/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/py_strategy_mid.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_struct.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/zlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main_new',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/main_new.py',
   'PYSOURCE'),
  ('libpython3.10.dylib',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/libpython3.10.dylib',
   'BINARY'),
  ('lib-dynload/binascii.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/binascii.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_statistics.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_contextvars.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_decimal.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/grp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_lzma.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_bz2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_opcode.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_pickle.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_hashlib.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha3.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_blake2.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha256.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_md5.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha1.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_sha512.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_random.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_bisect.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/math.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/unicodedata.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_datetime.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/array.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/select.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_socket.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_csv.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/resource.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_posixsubprocess.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/fcntl.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_queue.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_scproxy.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/termios.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_ssl.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/pyexpat.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_ctypes.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/readline.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/mmap.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_posixshmem.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_multiprocessing.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/syslog.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_json.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_heapq.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_multibytecodec.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_jp.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_kr.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_iso2022.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_cn.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_tw.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_codecs_hk.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_asyncio.cpython-310-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-310-darwin.so',
   '/Users/<USER>/.pyenv/versions/3.10.12/lib/python3.10/lib-dynload/_uuid.cpython-310-darwin.so',
   'EXTENSION'),
  ('pydantic_core/_pydantic_core.cpython-310-darwin.so',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/pydantic_core/_pydantic_core.cpython-310-darwin.so',
   'EXTENSION'),
  ('websockets/speedups.cpython-310-darwin.so',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets/speedups.cpython-310-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libssl.3.dylib',
   'BINARY'),
  ('libreadline.8.dylib',
   '/opt/homebrew/opt/readline/lib/libreadline.8.dylib',
   'BINARY'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/certifi/py.typed',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('websockets-13.0.dist-info/top_level.txt',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/top_level.txt',
   'DATA'),
  ('websockets-13.0.dist-info/RECORD',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('structlog-25.4.0.dist-info/METADATA',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/METADATA',
   'DATA'),
  ('websockets-13.0.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('structlog-25.4.0.dist-info/WHEEL',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/WHEEL',
   'DATA'),
  ('structlog-25.4.0.dist-info/INSTALLER',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/INSTALLER',
   'DATA'),
  ('websockets-13.0.dist-info/WHEEL',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/WHEEL',
   'DATA'),
  ('structlog-25.4.0.dist-info/RECORD',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/RECORD',
   'DATA'),
  ('websockets-13.0.dist-info/METADATA',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/METADATA',
   'DATA'),
  ('structlog-25.4.0.dist-info/REQUESTED',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/REQUESTED',
   'DATA'),
  ('structlog-25.4.0.dist-info/licenses/LICENSE-APACHE',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/licenses/LICENSE-APACHE',
   'DATA'),
  ('websockets-13.0.dist-info/REQUESTED',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('websockets-13.0.dist-info/LICENSE',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/websockets-13.0.dist-info/LICENSE',
   'DATA'),
  ('structlog-25.4.0.dist-info/licenses/LICENSE-MIT',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/licenses/LICENSE-MIT',
   'DATA'),
  ('structlog-25.4.0.dist-info/licenses/NOTICE',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/structlog-25.4.0.dist-info/licenses/NOTICE',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/.env/lib/python3.10/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Desktop/py_strategy/py_strategy_mid/build/py_strategy_mid/base_library.zip',
   'DATA')],
 'libpython3.10.dylib',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
