ts=2025-07-20T18:28:16.970830 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-20T18:28:16.970872 level=info event="init py strategy mid server" module=main_new lineno=81 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-20T18:28:16.973822 level=info event="py strategy mid server init complete" module=main_new lineno=93
ts=2025-07-20T18:28:16.973846 level=info event="starting py strategy mid server" module=main_new lineno=330
ts=2025-07-20T18:28:16.973864 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-20T18:28:16.973882 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-20T18:28:16.973909 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-20T18:28:16.973933 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-20T18:28:16.973952 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-20T18:28:16.973972 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-20T18:28:16.973990 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-20T18:28:16.974004 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-20T18:28:16.974021 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-20T18:28:16.974146 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-20T18:28:16.974165 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-20T18:28:16.974219 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:28:16.974237 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:28:16.974251 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:28:16.974264 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:28:16.974314 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-20T18:28:16.974331 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-20T18:28:16.974360 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-20T18:28:16.974383 level=info event="strategy mid server started" module=main_new lineno=343
ts=2025-07-20T18:28:16.974397 level=info event="starting http server" module=main_new lineno=27 host=0.0.0.0 port=8766
ts=2025-07-20T18:28:16.974639 level=info event="http server started" module=main_new lineno=31 host=0.0.0.0 port=8766
ts=2025-07-20T18:28:16.974661 level=info event="http server started" module=main_new lineno=356
ts=2025-07-20T18:28:16.974680 level=info event="starting websocket server" module=main_new lineno=56 host=0.0.0.0 port=8765
ts=2025-07-20T18:28:16.974880 level=info event="websocket server started" module=main_new lineno=60 host=0.0.0.0 port=8765
ts=2025-07-20T18:28:16.974910 level=info event="websocket server started" module=main_new lineno=371
ts=2025-07-20T18:28:16.974946 level=info event="py strategy mid server started success" module=main_new lineno=381 active_status=true
ts=2025-07-20T18:28:19.355279 level=info event="signal received" module=main_new lineno=415 signum=2
ts=2025-07-20T18:28:19.355369 level=info event="stopping py strategy mid server" module=main_new lineno=385
ts=2025-07-20T18:28:19.355394 level=info event="http server stopped" module=main_new lineno=43
ts=2025-07-20T18:28:19.355413 level=info event="http server stopped" module=main_new lineno=390
ts=2025-07-20T18:28:19.355430 level=info event="websocket server stopped" module=main_new lineno=72
ts=2025-07-20T18:28:19.355449 level=info event="websocket server stopped" module=main_new lineno=397
ts=2025-07-20T18:28:19.355655 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-20T18:28:19.355686 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-20T18:28:19.355736 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-20T18:28:19.355755 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-20T18:28:19.355799 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-20T18:28:19.355820 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-20T18:28:19.355862 level=info event="strategy mid server stopped" module=main_new lineno=406
ts=2025-07-20T18:28:19.355880 level=info event="py strategy mid server stopped success" module=main_new lineno=411 active_status=false
ts=2025-07-20T18:28:19.355898 level=info event="exit py strategy mid server" module=main_new lineno=418
ts=2025-07-20T18:30:06.188594 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-20T18:30:06.188882 level=info event="init py strategy mid server" module=main_new lineno=81 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-20T18:30:06.196986 level=info event="py strategy mid server init complete" module=main_new lineno=93
ts=2025-07-20T18:30:06.197075 level=info event="starting py strategy mid server" module=main_new lineno=330
ts=2025-07-20T18:30:06.197121 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-20T18:30:06.197161 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-20T18:30:06.197228 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-20T18:30:06.197272 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-20T18:30:06.197311 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-20T18:30:06.197349 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-20T18:30:06.197385 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-20T18:30:06.197417 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-20T18:30:06.197471 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-20T18:30:06.197768 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-20T18:30:06.197803 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-20T18:30:06.197911 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:30:06.197951 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:30:06.197985 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:30:06.198044 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-20T18:30:06.198122 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-20T18:30:06.198159 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-20T18:30:06.198211 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-20T18:30:06.198263 level=info event="strategy mid server started" module=main_new lineno=343
ts=2025-07-20T18:30:06.198296 level=info event="starting http server" module=main_new lineno=27 host=0.0.0.0 port=8766
ts=2025-07-20T18:30:06.198919 level=info event="http server started" module=main_new lineno=31 host=0.0.0.0 port=8766
ts=2025-07-20T18:30:06.198957 level=info event="http server started" module=main_new lineno=356
ts=2025-07-20T18:30:06.198989 level=info event="starting websocket server" module=main_new lineno=56 host=0.0.0.0 port=8765
ts=2025-07-20T18:30:06.200164 level=info event="websocket server started" module=main_new lineno=60 host=0.0.0.0 port=8765
ts=2025-07-20T18:30:06.200194 level=info event="websocket server started" module=main_new lineno=371
ts=2025-07-20T18:30:06.200219 level=info event="py strategy mid server started success" module=main_new lineno=381 active_status=true
ts=2025-07-20T18:30:11.337432 level=info event="signal received" module=main_new lineno=415 signum=2
ts=2025-07-20T18:30:11.337694 level=info event="stopping py strategy mid server" module=main_new lineno=385
ts=2025-07-20T18:30:11.337787 level=info event="http server stopped" module=main_new lineno=43
ts=2025-07-20T18:30:11.337841 level=info event="http server stopped" module=main_new lineno=390
ts=2025-07-20T18:30:11.337891 level=info event="websocket server stopped" module=main_new lineno=72
ts=2025-07-20T18:30:11.337935 level=info event="websocket server stopped" module=main_new lineno=397
ts=2025-07-20T18:30:11.338221 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-20T18:30:11.338302 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-20T18:30:11.338442 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-20T18:30:11.338487 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-20T18:30:11.338567 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-20T18:30:11.338617 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-20T18:30:11.338703 level=info event="strategy mid server stopped" module=main_new lineno=406
ts=2025-07-20T18:30:11.338752 level=info event="py strategy mid server stopped success" module=main_new lineno=411 active_status=false
ts=2025-07-20T18:30:11.338802 level=info event="exit py strategy mid server" module=main_new lineno=418
ts=2025-07-21T08:17:46.527731 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-21T08:17:46.527860 level=info event="init py strategy mid server" module=main_new lineno=81 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-21T08:17:46.530653 level=info event="py strategy mid server init complete" module=main_new lineno=93
ts=2025-07-21T08:17:46.530673 level=info event="starting py strategy mid server" module=main_new lineno=330
ts=2025-07-21T08:17:46.530690 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-21T08:17:46.530706 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-21T08:17:46.530730 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-21T08:17:46.530761 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-21T08:17:46.530776 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-21T08:17:46.530790 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-21T08:17:46.530804 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-21T08:17:46.530816 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-21T08:17:46.530832 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-21T08:17:46.530955 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-21T08:17:46.530968 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-21T08:17:46.531021 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:17:46.531037 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:17:46.531051 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:17:46.531063 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:17:46.531110 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-21T08:17:46.531125 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-21T08:17:46.531153 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-21T08:17:46.531175 level=info event="strategy mid server started" module=main_new lineno=343
ts=2025-07-21T08:17:46.531188 level=info event="starting http server" module=main_new lineno=27 host=0.0.0.0 port=8766
ts=2025-07-21T08:17:46.531407 level=info event="http server started" module=main_new lineno=31 host=0.0.0.0 port=8766
ts=2025-07-21T08:17:46.531426 level=info event="http server started" module=main_new lineno=356
ts=2025-07-21T08:17:46.531443 level=info event="starting websocket server" module=main_new lineno=56 host=0.0.0.0 port=8765
ts=2025-07-21T08:17:46.531606 level=info event="websocket server started" module=main_new lineno=60 host=0.0.0.0 port=8765
ts=2025-07-21T08:17:46.531651 level=info event="websocket server started" module=main_new lineno=371
ts=2025-07-21T08:17:46.531677 level=info event="py strategy mid server started success" module=main_new lineno=381 active_status=true
ts=2025-07-21T08:18:03.039382 level=info event="signal received" module=main_new lineno=429 signum=2
ts=2025-07-21T08:18:03.039733 level=info event="stopping py strategy mid server" module=main_new lineno=385
ts=2025-07-21T08:18:03.039769 level=info event="http server stopped" module=main_new lineno=43
ts=2025-07-21T08:18:03.039788 level=info event="http server stopped" module=main_new lineno=390
ts=2025-07-21T08:18:03.039807 level=info event="websocket server stopped" module=main_new lineno=72
ts=2025-07-21T08:18:03.039821 level=info event="websocket server stopped" module=main_new lineno=397
ts=2025-07-21T08:18:03.039907 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-21T08:18:03.039934 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-21T08:18:03.039976 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-21T08:18:03.039991 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-21T08:18:03.040040 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-21T08:18:03.040062 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-21T08:18:03.040097 level=info event="strategy mid server stopped" module=main_new lineno=416
ts=2025-07-21T08:18:03.040155 level=info event="py strategy mid server stopped success" module=main_new lineno=425 active_status=false
ts=2025-07-21T08:18:03.040174 level=info event="exit py strategy mid server" module=main_new lineno=432
ts=2025-07-21T08:24:12.738495 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-21T08:24:12.738698 level=info event="init py strategy mid server" module=main_new lineno=82 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-21T08:24:12.741545 level=info event="py strategy mid server init complete" module=main_new lineno=97
ts=2025-07-21T08:24:12.741567 level=info event="starting py strategy mid server" module=main_new lineno=334
ts=2025-07-21T08:24:12.741583 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-21T08:24:12.741598 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-21T08:24:12.741622 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-21T08:24:12.741639 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-21T08:24:12.741654 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-21T08:24:12.741668 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-21T08:24:12.741682 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-21T08:24:12.741695 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-21T08:24:12.741711 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-21T08:24:12.741830 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-21T08:24:12.741843 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-21T08:24:12.741892 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:24:12.741908 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:24:12.741921 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:24:12.741934 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:24:12.741979 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-21T08:24:12.741998 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-21T08:24:12.742029 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-21T08:24:12.742054 level=info event="strategy mid server started" module=main_new lineno=346
ts=2025-07-21T08:24:12.742071 level=info event="starting http server" module=main_new lineno=28 host=0.0.0.0 port=8766
ts=2025-07-21T08:24:12.742206 level=info event="http server started" module=main_new lineno=32 host=0.0.0.0 port=8766
ts=2025-07-21T08:24:12.742354 level=info event="http server started" module=main_new lineno=359
ts=2025-07-21T08:24:12.742389 level=info event="starting websocket server" module=main_new lineno=57 host=0.0.0.0 port=8765
ts=2025-07-21T08:24:12.742592 level=info event="websocket server started" module=main_new lineno=61 host=0.0.0.0 port=8765
ts=2025-07-21T08:24:12.742611 level=info event="websocket server started" module=main_new lineno=374
ts=2025-07-21T08:24:12.742642 level=info event="py strategy mid server started success" module=main_new lineno=384 active_status=true
ts=2025-07-21T08:24:23.233786 level=info event="signal received" module=main_new lineno=430 signum=2
ts=2025-07-21T08:24:23.234059 level=info event="stopping py strategy mid server" module=main_new lineno=388
ts=2025-07-21T08:24:23.234161 level=info event="http server stopped" module=main_new lineno=44
ts=2025-07-21T08:24:23.234218 level=info event="http server stopped" module=main_new lineno=393
ts=2025-07-21T08:24:23.234268 level=info event="websocket server stopped" module=main_new lineno=73
ts=2025-07-21T08:24:23.234305 level=info event="websocket server stopped" module=main_new lineno=400
ts=2025-07-21T08:24:23.234367 level=info event="using saved event loop for shutdown" module=main_new lineno=408
ts=2025-07-21T08:24:23.234659 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-21T08:24:23.234726 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-21T08:24:23.234852 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-21T08:24:23.234898 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-21T08:24:23.234974 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-21T08:24:23.235033 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-21T08:24:23.235278 level=info event="strategy mid server stopped" module=main_new lineno=411
ts=2025-07-21T08:24:23.235328 level=info event="py strategy mid server stopped success" module=main_new lineno=426 active_status=false
ts=2025-07-21T08:24:23.235377 level=info event="exit py strategy mid server" module=main_new lineno=433
ts=2025-07-21T08:37:59.369819 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-21T08:37:59.369945 level=info event="init py strategy mid server" module=main lineno=24 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-21T08:37:59.373010 level=info event="py strategy mid server init complete" module=main lineno=38
ts=2025-07-21T08:37:59.373139 level=info event="starting py strategy mid server" module=main lineno=292
ts=2025-07-21T08:37:59.373159 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-21T08:37:59.373175 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-21T08:37:59.373203 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-21T08:37:59.373220 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-21T08:37:59.373236 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-21T08:37:59.373250 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-21T08:37:59.373265 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-21T08:37:59.373278 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-21T08:37:59.373300 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-21T08:37:59.373322 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-21T08:37:59.373333 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-21T08:37:59.373386 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:37:59.373401 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:37:59.373414 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:37:59.373426 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:37:59.373473 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-21T08:37:59.373489 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-21T08:37:59.373500 level=info event="strategy mid server started" module=main lineno=301
ts=2025-07-21T08:37:59.373654 level=info event="http server started" module=main lineno=316
ts=2025-07-21T08:37:59.373751 level=info event="websocket server started" module=main lineno=333
ts=2025-07-21T08:37:59.373764 level=info event="py strategy mid server started success" module=main lineno=343 active_status=true
ts=2025-07-21T08:37:59.373777 level=info event="service started, waiting for shutdown signal" module=main lineno=430
ts=2025-07-21T08:37:59.373811 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-21T08:38:14.760766 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:38:14.760839 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:14.760867 level=info event="cancelling background tasks" module=main lineno=352 count=2
ts=2025-07-21T08:38:14.761010 level=info event="background tasks cancelled" module=main lineno=360
ts=2025-07-21T08:38:14.761048 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:14.761072 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:14.761090 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:14.761168 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-21T08:38:14.761200 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-21T08:38:14.761243 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-21T08:38:14.761260 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-21T08:38:14.761295 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-21T08:38:14.761315 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-21T08:38:14.761367 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:14.761383 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:14.761402 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:38:14.861728 level=info event="shutting down services" module=main lineno=448
ts=2025-07-21T08:38:14.861841 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:14.861875 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:14.861900 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:14.861922 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:14.862036 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:14.862076 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:14.862221 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:38:14.862245 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:14.862267 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:14.862286 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:14.862306 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:14.862398 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:14.862421 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:14.862443 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:38:14.862729 level=info event="main function completed" module=main lineno=461
ts=2025-07-21T08:38:22.096575 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-21T08:38:22.096616 level=info event="init py strategy mid server" module=main lineno=24 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-21T08:38:22.099592 level=info event="py strategy mid server init complete" module=main lineno=38
ts=2025-07-21T08:38:22.099716 level=info event="starting py strategy mid server" module=main lineno=292
ts=2025-07-21T08:38:22.099734 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-21T08:38:22.099750 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-21T08:38:22.099777 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-21T08:38:22.099793 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-21T08:38:22.099809 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-21T08:38:22.099823 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-21T08:38:22.099837 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-21T08:38:22.099849 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-21T08:38:22.099869 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-21T08:38:22.099882 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-21T08:38:22.099893 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-21T08:38:22.099941 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:22.099956 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:22.099970 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:22.099983 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:22.100030 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-21T08:38:22.100046 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-21T08:38:22.100066 level=info event="strategy mid server started" module=main lineno=301
ts=2025-07-21T08:38:22.100211 level=info event="http server started" module=main lineno=316
ts=2025-07-21T08:38:22.100306 level=info event="websocket server started" module=main lineno=333
ts=2025-07-21T08:38:22.100320 level=info event="py strategy mid server started success" module=main lineno=343 active_status=true
ts=2025-07-21T08:38:22.100332 level=info event="service started, waiting for shutdown signal" module=main lineno=430
ts=2025-07-21T08:38:22.100368 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-21T08:38:23.735631 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:38:23.735892 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:23.736008 level=info event="cancelling background tasks" module=main lineno=352 count=2
ts=2025-07-21T08:38:23.736414 level=info event="background tasks cancelled" module=main lineno=360
ts=2025-07-21T08:38:23.736553 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:23.736641 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:23.736716 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:23.736966 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-21T08:38:23.737042 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-21T08:38:23.737132 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-21T08:38:23.737179 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-21T08:38:23.737255 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-21T08:38:23.737319 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-21T08:38:23.737430 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:23.737477 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:23.737528 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:38:23.818134 level=info event="shutting down services" module=main lineno=448
ts=2025-07-21T08:38:23.818381 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:23.818485 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:23.818560 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:23.818632 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:23.818953 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:23.819037 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:23.819425 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:38:23.819521 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:23.819596 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:23.819668 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:23.819737 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:23.819964 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:23.820040 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:23.820114 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:38:23.820880 level=info event="main function completed" module=main lineno=461
ts=2025-07-21T08:38:31.005511 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-21T08:38:31.005557 level=info event="init py strategy mid server" module=main lineno=24 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-21T08:38:31.008488 level=info event="py strategy mid server init complete" module=main lineno=38
ts=2025-07-21T08:38:31.008647 level=info event="starting py strategy mid server" module=main lineno=292
ts=2025-07-21T08:38:31.008666 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-21T08:38:31.008682 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-21T08:38:31.008710 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-21T08:38:31.008728 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-21T08:38:31.008744 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-21T08:38:31.008758 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-21T08:38:31.008773 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-21T08:38:31.008786 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-21T08:38:31.008806 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-21T08:38:31.008820 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-21T08:38:31.008832 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-21T08:38:31.008881 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:31.008897 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:31.008911 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:31.008924 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:38:31.008974 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-21T08:38:31.008990 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-21T08:38:31.009001 level=info event="strategy mid server started" module=main lineno=301
ts=2025-07-21T08:38:31.009157 level=info event="http server started" module=main lineno=316
ts=2025-07-21T08:38:31.009256 level=info event="websocket server started" module=main lineno=333
ts=2025-07-21T08:38:31.009270 level=info event="py strategy mid server started success" module=main lineno=343 active_status=true
ts=2025-07-21T08:38:31.009284 level=info event="service started, waiting for shutdown signal" module=main lineno=430
ts=2025-07-21T08:38:31.009321 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-21T08:38:50.732910 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:38:50.732979 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:50.733005 level=info event="cancelling background tasks" module=main lineno=352 count=2
ts=2025-07-21T08:38:50.733118 level=info event="background tasks cancelled" module=main lineno=360
ts=2025-07-21T08:38:50.733142 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:50.733176 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:50.733192 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:50.733249 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-21T08:38:50.733275 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-21T08:38:50.733318 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-21T08:38:50.733335 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-21T08:38:50.733369 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-21T08:38:50.733388 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-21T08:38:50.733442 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:50.733458 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:50.733476 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:38:50.833918 level=info event="shutting down services" module=main lineno=448
ts=2025-07-21T08:38:50.834001 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:50.834032 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:50.834056 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:50.834078 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:50.834195 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:50.834222 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:50.834361 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:38:50.834386 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:38:50.834407 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:38:50.834426 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:38:50.834445 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:38:50.834546 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:38:50.834571 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:38:50.834592 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:38:50.834883 level=info event="main function completed" module=main lineno=461
ts=2025-07-21T08:40:37.760691 level=info event="init struct flogger" module=config lineno=111
ts=2025-07-21T08:40:37.761152 level=info event="init py strategy mid server" module=main lineno=24 config="AppConfig(mid_server=MidServerConfig(host='0.0.0.0', ws_port=8765, http_port=8766), log=LogConfig(log_level='INFO', log_file='./log/py_strategy_mid_server.log'), strategy_engines=[])"
ts=2025-07-21T08:40:37.766679 level=info event="py strategy mid server init complete" module=main lineno=38
ts=2025-07-21T08:40:37.766972 level=info event="starting py strategy mid server" module=main lineno=292
ts=2025-07-21T08:40:37.767157 level=info event="init strategy mid server" module=mid_server lineno=25 strategy_engines=[]
ts=2025-07-21T08:40:37.767217 level=info event="init mdb tables" module=table_manager lineno=20
ts=2025-07-21T08:40:37.767289 level=info event="registering table" module=mdb lineno=101 query_fields=['strategy_instance_id'] table=py_strategy_instance_field
ts=2025-07-21T08:40:37.767327 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'strategy_engine_id']" table=py_strategy_deploy_field
ts=2025-07-21T08:40:37.767387 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'param_key']" table=py_strategy_param_field
ts=2025-07-21T08:40:37.767416 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_name', 'enum_name']" table=py_strategy_enum_field
ts=2025-07-21T08:40:37.767444 level=info event="registering table" module=mdb lineno=101 query_fields="['strategy_instance_id', 'param_key']" table=py_strategy_instance_param_field
ts=2025-07-21T08:40:37.767469 level=info event="finish init mdb tables" module=table_manager lineno=54
ts=2025-07-21T08:40:37.767504 level=info event="strategy mid server init complete" module=mid_server lineno=47 strategy_engines_info={}
ts=2025-07-21T08:40:37.767530 level=info event="starting strategy mid server" module=mid_server lineno=70
ts=2025-07-21T08:40:37.767551 level=info event="starting initial data fetch from all strategy engines" module=mid_server lineno=103
ts=2025-07-21T08:40:37.767647 level=info event="no strategy_deploy data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:40:37.767677 level=info event="no strategy_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:40:37.767702 level=info event="no strategy_instances data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:40:37.767725 level=info event="no strategy_instance_parameters data returned from any strategy engine" module=data_fetch_service lineno=122
ts=2025-07-21T08:40:37.767788 level=info event="initial data fetch completed successfully" module=mid_server lineno=106
ts=2025-07-21T08:40:37.767814 level=info event="heartbeat check service started" module=heartbeat_check_service lineno=23
ts=2025-07-21T08:40:37.767832 level=info event="strategy mid server started" module=main lineno=301
ts=2025-07-21T08:40:37.768124 level=info event="http server started" module=main lineno=316
ts=2025-07-21T08:40:37.768297 level=info event="websocket server started" module=main lineno=333
ts=2025-07-21T08:40:37.768321 level=info event="py strategy mid server started success" module=main lineno=343 active_status=true
ts=2025-07-21T08:40:37.768343 level=info event="service started, waiting for shutdown signal" module=main lineno=430
ts=2025-07-21T08:40:37.768399 level=info event="heartbeat check loop started" module=heartbeat_check_service lineno=40
ts=2025-07-21T08:42:50.436942 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:42:50.437910 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:42:50.437948 level=info event="cancelling background tasks" module=main lineno=352 count=2
ts=2025-07-21T08:42:50.438080 level=info event="background tasks cancelled" module=main lineno=360
ts=2025-07-21T08:42:50.438109 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:42:50.438133 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:42:50.438154 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:42:50.438225 level=info event="stopping strategy mid server" module=mid_server lineno=83
ts=2025-07-21T08:42:50.438250 level=info event="heartbeat check service stopped" module=heartbeat_check_service lineno=30
ts=2025-07-21T08:42:50.438328 level=info event="heartbeat check loop cancelled" module=heartbeat_check_service lineno=47
ts=2025-07-21T08:42:50.438385 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=53
ts=2025-07-21T08:42:50.438449 level=info event="heartbeat check loop stopped" module=heartbeat_check_service lineno=37
ts=2025-07-21T08:42:50.438490 level=info event="strategy mid server stopped" module=mid_server lineno=99
ts=2025-07-21T08:42:50.438571 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:42:50.438600 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:42:50.438644 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:42:50.537793 level=info event="shutting down services" module=main lineno=448
ts=2025-07-21T08:42:50.537874 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:42:50.537904 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:42:50.537930 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:42:50.537954 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:42:50.538079 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:42:50.538107 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:42:50.538262 level=info event="lifespan cleanup started" module=main lineno=285
ts=2025-07-21T08:42:50.538291 level=info event="stopping py strategy mid server" module=main lineno=347
ts=2025-07-21T08:42:50.538315 level=info event="http server stopped" module=main lineno=368
ts=2025-07-21T08:42:50.538338 level=info event="websocket server stopped" module=main lineno=376
ts=2025-07-21T08:42:50.538359 level=info event="server tasks will be cancelled with background tasks" module=main lineno=381
ts=2025-07-21T08:42:50.538466 level=info event="strategy mid server stopped" module=main lineno=387
ts=2025-07-21T08:42:50.538491 level=info event="py strategy mid server stopped success" module=main lineno=394 active_status=false
ts=2025-07-21T08:42:50.538516 level=info event="lifespan cleanup completed" module=main lineno=288
ts=2025-07-21T08:42:50.538847 level=info event="main function completed" module=main lineno=461
