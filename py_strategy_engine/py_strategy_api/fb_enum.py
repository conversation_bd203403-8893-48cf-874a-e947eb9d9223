'''
CFFEX Confidential.

@Copyright 2018 CFFEX.  All rights reserved.

The source code for this program is not published or otherwise
divested of its trade secrets, irrespective of what has been
deposited with the China Copyright Office.

Author:  Febao
Date:    2025-02-18 15:16:20
Version: 0.0.7
'''
from enum import Enum


class table_enum(Enum):
    """表格枚举"""
    exchange = 0X006F  # 产品信息
    product = 0X0008  # 产品信息
    option_serial = 0X0009  # 期权系列信息及基础属性
    security_instrument = 0X000C  # 现货合约
    option_instrument = 0X000B  # 期权合约
    future_instrument = 0X000A  # 期货合约信息及基础属性
    user = 0X0001  # 用户、用户角色基础信息
    market_data = 0X0015  # 原始市场行情
    order = 0X004E  # order指令流水
    trade = 0X004F  # 成交流水
    quote = 0X0050  # 报价流水
    position = 0X0039  # 合约持仓信息维护
    strategy_instance = 0X005D  # 策略实例维护
    instrument_param_value = 0X0052  # 合约参数参数值
    custom_param_value = 0X0053  # 自定义参数参数值
    trading_account = 0X0054  # 交易账户


class StrategyStateEnum(Enum):
    '''None'''
    STRATEGY_INIT_STAT = '1'  # 初始状态
    STRATEGY_RUNNING_STAT = '2'  # 运行中
    STRATEGY_PAUSE_STAT = '3'  # 已暂停
    STRATEGY_DELETE_STAT = '4'  # 已删除
    STRATEGY_TIMEOUT_STAT = '5'  # 已失效


class StrategyPauseReasonEnum(Enum):
    '''None'''
    PAUSE_BY_UNKNOWN = '0'  # 未知
    PAUSE_BY_USER_OPERATOR = '1'  # 用户手动暂停
    PAUSE_BY_EMERGENCY = '2'  # 紧急制动暂停
    PAUSE_BY_STRATEGY_OPERATOR = '3'  # 策略主动暂停
    PAUSE_BY_TIMEOUT = '4'  # 中后台连接超时暂停


class DirectionEnum(Enum):
    '''None'''
    DIRECTION_BUY = '0'  # 买
    DIRECTION_SELL = '1'  # 卖


class OffsetFlagEnum(Enum):
    '''None'''
    OFFSET_FLAG_AUTO = '5'  # 自动
    OFFSET_FLAG_OPEN = '1'  # 开
    OFFSET_FLAG_CLOSE = '2'  # 平
    OFFSET_FLAG_CLOSE_TODAY = '3'  # 平今
    OFFSET_FLAG_CLOSE_YESTERDAY = '4'  # 平昨


class HedgeFlagEnum(Enum):
    '''None'''
    HEDGE_FLAG_SPECULATION = '1'  # 投机
    HEDGE_FLAG_ARBITRAGE = '2'  # 套利
    HEDGE_FLAG_HEDGE = '3'  # 套保
    HEDGE_FLAG_MARKET_MAKER = '4'  # 做市商


class PriceCategoryEnum(Enum):
    '''None'''
    PRICE_CATEGORY_LIMIT = '2'  # 限价
    PRICE_CATEGORY_ANY = '1'  # 任意价
    PRICE_CATEGORY_BEST = '3'  # 对手方最优价
    PRICE_CATEGORY_FIVE_LEVEL = '4'  # 最优五档
    PRICE_CATEGORY_ARBITRAGE = '5'  # 套利
    PRICE_CATEGORY_SWAP = '6'  # 互换
    PRICE_CATEGORY_BOTH = '7'  # 报价衍生
    PRICE_CATEGORY_OWN_BEST = '9'  # 本方最优价


class TimeConditionEnum(Enum):
    '''None'''
    TIME_CONDITION_GFD = '2'  # 当日有效
    TIME_CONDITION_IOC = '1'  # 立即完成否则撤销
    TIME_CONDITION_GIS = '3'  # 小节有效


class VolumeConditionEnum(Enum):
    '''None'''
    VOLUME_ANY = '1'  # 任何数量
    VOLUME_COMPLETE = '2'  # 全部数量


class OrderStatusEnum(Enum):
    '''None'''
    ORDER_STATUS_NONE = '0'  # 未知状态
    ORDER_STATUS_WAITING = '1'  # 已报单，待确认
    ORDER_STATUS_IN_BOOK = '2'  # 交易所确认
    ORDER_STATUS_CANCEL = '3'  # 订单取消
    ORDER_STATUS_ERROR = '4'  # 订单错误
    ORDER_STATUS_PART_TRADED = '5'  # 部分成交
    ORDER_STATUS_ALL_TRADED = '6'  # 全部成交
    ORDER_STATUS_TIMEOUT = '7'  # 超时状态


class ExchangeIdEnum(Enum):
    '''None'''
    EXCHANGE_UNKNOWN = 'n'  # 未知
    EXCHANGE_CFFEX = '0'  # 中金所
    EXCHANGE_SHFE = '1'  # 上期所
    EXCHANGE_DCE = '2'  # 大商所
    EXCHANGE_ZCE = '3'  # 郑商所
    EXCHANGE_SSE = '4'  # 上交所
    EXCHANGE_SZSE = '5'  # 深交所
    EXCHANGE_INE = '6'  # 能源交易中心
    EXCHANGE_GFEX = '7'  # 广期所
    EXCHANGE_BSE = '8'  # 北交所
    EXCHANGE_ALL = 'z'  # 全部


class OrderSourceEnum(Enum):
    '''None'''
    ORDER_UNKNOWN = 'u'  # 未知
    ORDER_SOURCE_ALL = 'A'  # 全部
    ORDER_SOURCE_MANUAL_ORDER = '1'  # 手动报单
    ORDER_SOURCE_MANUAL_QUOTE_ORDER = '2'  # 手动报价衍生单
    ORDER_SOURCE_STRATEGY_ORDER = '3'  # 策略报单
    ORDER_SOURCE_STRATEGY_QUOTE_ORDER = '4'  # 策略报价衍生单
    ORDER_SOURCE_OUTSIDE_ORDER = '5'  # 外部报单
    ORDER_SOURCE_OUTSIDE_QUOTE_ORDER = '6'  # 外部报价衍生单


class IntStatusEnum(Enum):
    '''None'''
    INACTIVE = '0'  # 非活跃
    ACTIVE = '1'  # 活跃


class SecurityClassEnum(Enum):
    '''None'''
    SECURITY_UNKNOWN = 'n'  # 未配置
    SECURITY_FUND = '0'  # 基金
    SECURITY_STOCK = '1'  # 股票
    SECURITY_BOND = '2'  # 债券


class SecuritySubClassEnum(Enum):
    '''None'''
    SECURITY_SUB_UNKNOWN = 'n'  # 未配置
    FUND_ETF = '0'  # ETF基金
    FUND_RET = '1'  # RET基金
    STOCK_KSH = '2'  # 科创板股票
    STOCK_BASE = '3'  # 基础层股票
    STOCK_INNOVATION = '4'  # 创新层股票
    STOCK_SELECT = '5'  # 精选层股票


class TradingTypeEnum(Enum):
    '''None'''
    T_UNKNOWN = 'n'  # 未配置
    T_0 = '0'  # T+0
    T_1 = '1'  # T+1


class ExpireDateTypeEnum(Enum):
    '''None'''
    RECENT_MONTH = '1'  # 当月合约
    FAR_MONTH = '2'  # 远月合约


class ExerciseTypeEnum(Enum):
    '''None'''
    PHYSICAL_DELIVERY = '1'  # 实物交割
    CASH_DELIVERY = '2'  # 现金交割


class OptionTypeEnum(Enum):
    '''None'''
    OPTION_CALL = 'c'  # 认购期权
    OPTION_PUT = 'p'  # 认沽期权


class ExerciseStyleEnum(Enum):
    '''None'''
    EXERCISE_STYLE_AMERICAN = '0'  # 美式
    EXERCISE_STYLE_EUROPEAN = '1'  # 欧式


class LastOperateSourceEnum(Enum):
    '''None'''
    TERMINAL = '0'  # 客户端
    STRATEGY = '1'  # 策略


class AuthorityCategoryEnum(Enum):
    '''None'''
    READ = '0'  # 策略参数只读属性
    READ_WRITE = '1'  # 策略参数读写属性


class BoolEnum(Enum):
    '''None'''
    MM_FALSE = '0'  # false
    MM_TRUE = '1'  # true


class TradeSourceEnum(Enum):
    '''None'''
    TRADE_SOURCE_UNKNOWN = 'u'  # 未知
    TRADE_SOURCE_INIT = '1'  # 初始化
    TRADE_SOURCE_MANUAL = '2'  # 手动单
    TRADE_SOURCE_STRATEGY = '3'  # 策略单
    TRADE_SOURCE_OUTSIDE = '4'  # 外部流水
    TRADE_SOURCE_OUTSIDE_COMB = '5'  # 外部流水组合
    TRADE_SOURCE_BOOK = '6'  # 簿记
    TRADE_SOURCE_CREATION_REDEMPTION = '7'  # 簿记申赎
    TRADE_SOURCE_OUTSIDE_CREATION_REDEMPTION = '8'  # 外部流水申赎
    TRADE_SOURCE_SETTLEMENT = 'A'  # 结算成交
    TRADE_SOURCE_MARGIN_TRADING = 'B'  # 簿记融券可卖
