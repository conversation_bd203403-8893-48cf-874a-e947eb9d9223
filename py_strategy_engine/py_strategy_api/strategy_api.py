from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from .fb_models import *
from .fb_enum import table_enum


class StrategyApi(ABC):
    strategy_version: str = ""
    instance_params: list = []

    def __init__(self, engine):
        self._api_version = "1.0.0"
        self._engine = engine
        self._strategy_id = 0

    def on_init(self) -> None:
        """策略初始化"""
        pass

    def on_start(self) -> None:
        """策略启动"""
        # 策略状态由策略引擎维护，不在这里设置
        pass

    def on_stop(self) -> None:
        """策略停止"""
        # 策略状态由策略引擎维护，不在这里设置
        pass

    def on_market_data(self, tick: MarketDataMsg) -> None:
        """市场数据回调"""
        pass

    def on_order(self, order: OrderMsg) -> None:
        """订单回调"""
        pass

    def on_trade(self, trade: TradeMsg) -> None:
        """成交回调"""
        pass

    def on_instrument_param_value(self, instrument_param_value: InstrumentParamValueMsg) -> None:
        """合约参数值回调"""
        pass

    def on_custom_param_value(self, custom_param_value: CustomParamValueMsg) -> None:
        """自定义参数值回调"""
        pass

    def on_trading_account(self, trading_account: TradingAccountMsg) -> None:
        """交易账户回调"""
        pass

    def on_future_instrument(self, data: FutureInstrumentMsg) -> None:
        """期货合约回调"""
        pass

    def on_security_instrument(self, data: SecurityInstrumentMsg) -> None:
        """现货合约回调"""
        pass

    def on_option_instrument(self, data: OptionInstrumentMsg) -> None:
        """期权合约回调"""
        pass

    def on_position(self, data: PositionMsg) -> None:
        """持仓回调"""
        pass

    def insert_order(self, order: FbOrderEntity) -> int:
        """发送订单"""
        return self._engine.insert_order(self._strategy_id, order)

    def cancel_order(self, order: FbCancelOrderEntity) -> bool:
        """撤销订单"""
        return self._engine.cancel_order(self._strategy_id, order)

    def modify_instrument_param_value(self, instrument_param_value: FbInstrumentParamValueEntity) -> bool:
        """修改合约参数"""
        return self._engine.modify_instrument_param_value(self._strategy_id, instrument_param_value)

    def modify_custom_param_value(self, custom_param_value: FbCustomParamValueEntity) -> bool:
        """修改自定义参数"""
        return self._engine.modify_custom_param_value(self._strategy_id, custom_param_value)

    def get_from_mdb(self, strategy_id: int, table: table_enum, **kwargs) -> Union[Any, List[Any], None]:
        """
        从内存表查询数据
        1.**kwargs代表筛选条件,只有primary_key可以作为筛选条件,填写其他无效筛选条件会返回None
        2. 仅输入strategy_id和table参数则会获取表中全量信息

        MarketData(primary_key=instrument_id)
        筛选  data: MarketDataMsg = self.get_from_mdb(strategy_id, table_enum.market_data, instrument_id="CFE_IF2412")
        全量  data: List[MarketDataMsg] = self.get_from_mdb(strategy_id, table_enum.market_data)

        User(primary_key=user_name)
        筛选  data: UserMsg = self.get_from_mdb(strategy_id, table_enum.user, user_name="nick")
        全量  data: List[UserMsg] = self.get_from_mdb(strategy_id, table_enum.user)

        Order(primary_key=order_id)
        筛选  data: OrderMsg = self.get_from_mdb(strategy_id, table_enum.order, order_id="123456")
        全量  data: List[OrderMsg] = self.get_from_mdb(strategy_id, table_enum.order)

        Trade(primary_key=trade_id)
        筛选  data: TradeMsg = self.get_from_mdb(strategy_id, table_enum.trade, trade_id="123456")
        全量  data: List[TradeMsg] = self.get_from_mdb(strategy_id, table_enum.trade)

        SecurityInstrument(primary_key=instrument_id)
        筛选  data: SecurityInstrumentMsg = self.get_from_mdb(strategy_id, table_enum.security_instrument, instrument_id="SSE_510050")
        全量  data: List[SecurityInstrumentMsg] = self.get_from_mdb(strategy_id, table_enum.security_instrument)

        FutureInstrument(primary_key=instrument_id)
        筛选  data: FutureInstrumentMsg = self.get_from_mdb(strategy_id, table_enum.future_instrument, instrument_id="CFE_IF2412")
        全量  data: List[FutureInstrumentMsg] = self.get_from_mdb(strategy_id, table_enum.future_instrument)

        OptionInstrument(primary_key=instrument_id)
        筛选  data: OptionInstrumentMsg = self.get_from_mdb(strategy_id, table_enum.option_instrument, instrument_id="CFE_IO2405-P-3850")
        全量  data: List[OptionInstrumentMsg] = self.get_from_mdb(strategy_id, table_enum.option_instrument)

        Position(primary_key=instrument_id&trading_account_id)
        筛选  data: PositionMsg = self.get_from_mdb(strategy_id, table_enum.position, instrument_id="SSE_510050", trading_account_id="123456")
        全量  data: List[PositionMsg] = self.get_from_mdb(strategy_id, table_enum.position)

        TradingAccount(primary_key=trading_account_id)
        筛选  data: TradingAccountMsg = self.get_from_mdb(strategy_id, table_enum.trading_account, trading_account_id="123456")
        全量  data: List[TradingAccountMsg] = self.get_from_mdb(strategy_id, table_enum.trading_account)

        """
        return self._engine.get_from_mdb(strategy_id, table, **kwargs)
