import json
import asyncio
import threading
import uvicorn
from fastapi import FastAPI
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
from common.event import Event, EventEngine
from web.http_response import <PERSON><PERSON><PERSON><PERSON>ponse, HttpResponseManager
from common.strategy_manager import StrategyManager
from common.strategy_instance_manager import StrategyInstanceManager
from common.constants import *
from utils.flog import flogger
from queue import Queue


class LoadStrategyRequest(BaseModel):
    file_path: str


class ModifyInstancePriorityRequest(BaseModel):
    strategy_instance_id: int
    strategy_instance_priority: int
    strategy_instance_name: str


class CreateStrategyInstanceRequest(BaseModel):
    strategy_name: str
    strategy_instance_name: str
    trading_account_id: int
    user_id: int
    params: Optional[Dict[str, Any]] = None


class StrategyInstanceIdRequest(BaseModel):
    strategy_instance_id: int


class UpdateStrategyInstanceParamRequest(BaseModel):
    strategy_instance_id: int
    param_key: str
    param_value: Any


class ApiResponseModel(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    request_id: Optional[int] = None


class HttpServer:
    """HTTP服务器，使用FastAPI异步处理HTTP请求"""

    def __init__(self, event_engine: EventEngine, strategy_manager: StrategyManager, strategy_instance_manager: StrategyInstanceManager, config: Dict[str, Any]):
        flogger.info("http server start init", config=config)
        self.event_engine = event_engine
        self.strategy_manager = strategy_manager
        self.strategy_instance_manager = strategy_instance_manager
        self.config = config
        self.host = config.get("host", "0.0.0.0")
        self.port = config.get("port", 5000)
        self.app = FastAPI(title="策略引擎API", description="策略引擎HTTP API接口")
        self.thread = None
        self.response_manager = HttpResponseManager()

        # add queue
        self.response_queue = Queue()
        self.response_task: asyncio.Task = None
        flogger.debug("response queue created")

        # add cors
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        flogger.debug("cors middleware added")

        @self.app.on_event("startup")
        async def stratup_event():
            flogger.debug("fastapi startup event")
            self.response_task = asyncio.create_task(
                self._process_response_queue())
            flogger.debug("response task created")

        @self.app.on_event("shutdown")
        async def shutdown_event():
            flogger.debug("fastapi shutdown event")
            if hasattr(self, "response_task") and self.response_task:
                self.response_task.cancel()
                try:
                    await self.response_task
                except asyncio.CancelledError:
                    pass
                flogger.debug("response task stopped")

        # 设置路由
        self.setup_routes()
        flogger.info("http server end init", host=self.host, port=self.port)

    def setup_routes(self) -> None:
        @self.app.get("/api/strategy/get_all_strategy_instances", response_model=ApiResponseModel)
        async def get_all_strategy_instances():
            flogger.debug(
                "request received", endpoint="/api/strategy/get_all_strategy_instances", method="GET")
            request_id = self.response_manager.generate_request_id()
            strategy_instances = self.strategy_instance_manager.get_all_strategy_instance_infos_include_delete()
            flogger.debug("strategy instances retrieved",
                          strategy_instances=strategy_instances)
            return ApiResponseModel(
                success=True,
                message="success",
                data=[strategy_instance.dict()
                      for strategy_instance in strategy_instances],
                request_id=request_id
            )

        @self.app.get("/api/strategy/get_strategy_instance", response_model=ApiResponseModel)
        async def get_strategy_instance(request: StrategyInstanceIdRequest):
            flogger.debug("request received", endpoint="/api/strategy/get_strategy_instance",
                          method="GET", strategy_instance_id=request.strategy_instance_id)
            request_id = self.response_manager.generate_request_id()
            strategy_instance = self.strategy_instance_manager.get_strategy_instance_info(
                request.strategy_instance_id)
            flogger.debug("strategy instance retrieved",
                          strategy_instance=strategy_instance)
            return ApiResponseModel(
                success=True,
                message="success",
                data=strategy_instance.dict() if strategy_instance else None,
                request_id=request_id
            )

        @self.app.get("/api/strategy/get_strategy_instance_parameters", response_model=ApiResponseModel)
        async def get_strategy_instance_parameters():
            flogger.debug("request received", endpoint="/api/strategy/get_strategy_instance_parameters",
                          method="GET")
            request_id = self.response_manager.generate_request_id()

            strategy_instance_params = self.strategy_instance_manager.get_all_strategy_instance_parameters()
            flogger.debug("all strategy instance params retrieved",
                          param_count=len(strategy_instance_params))

            return ApiResponseModel(
                success=True,
                message="success",
                data=[strategy_instance_param.dict()
                      for strategy_instance_param in strategy_instance_params],
                request_id=request_id
            )

        @self.app.get("/api/strategy/get_deployed_strategies", response_model=ApiResponseModel)
        async def get_deployed_strategies():
            flogger.debug(
                "request received", endpoint="/api/strategy/get_deployed_strategies", method="GET")
            request_id = self.response_manager.generate_request_id()
            strategy_deploys = self.strategy_manager.get_all_deployed_strategies()
            flogger.debug("strategy deploys retrieved",
                          strategy_deploys=strategy_deploys)
            return ApiResponseModel(
                success=True,
                message="success",
                data=[strategy.dict() for strategy in strategy_deploys],
                request_id=request_id
            )

        @self.app.get("/api/strategy/get_strategy_parameters", response_model=ApiResponseModel)
        async def get_strategy_parameters():
            flogger.debug("request received", endpoint="/api/strategy/get_strategy_parameters",
                          method="GET")
            request_id = self.response_manager.generate_request_id()

            strategy_params = self.strategy_manager.get_all_strategy_parameters()
            flogger.debug("all strategy parameters retrieved",
                          param_count=len(strategy_params))

            return ApiResponseModel(
                success=True,
                message="success",
                data=[param.dict() for param in strategy_params],
                request_id=request_id
            )

        @self.app.post("/api/strategy/load", response_model=ApiResponseModel)
        async def load_strategy(request: LoadStrategyRequest):
            flogger.debug("request received", endpoint="/api/strategy/load",
                          method="POST", file_path=request.file_path)
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/load")
            event = Event(EVENT_LOAD_STRATEGY, {
                "file_path": request.file_path,
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("load strategy request sent",
                          request_id=request_id, file_path=request.file_path)
            return await self.wait_for_response(future, request_id)

        @self.app.post("/api/strategy/create", response_model=ApiResponseModel)
        async def create_strategy_instance(request: CreateStrategyInstanceRequest):
            flogger.debug("request received", endpoint="/api/strategy/create", method="POST", strategy_name=request.strategy_name,
                          strategy_instance_name=request.strategy_instance_name, user_id=request.user_id, params=request.params or {})
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/create")
            event = Event(EVENT_CREATE_STRATEGY_INSTANCE, {
                "strategy_name": request.strategy_name,
                "strategy_instance_name": request.strategy_instance_name,
                "trading_account_id": request.trading_account_id,
                "user_id": request.user_id,
                "params": request.params or {},
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("create strategy instance request sent", request_id=request_id, strategy_name=request.strategy_name,
                          strategy_instance_name=request.strategy_instance_name, params=request.params or {})
            return await self.wait_for_response(future, request_id)

        @self.app.post("/api/strategy/start", response_model=ApiResponseModel)
        async def start_strategy_instance(request: StrategyInstanceIdRequest):
            flogger.debug("request received", endpoint="/api/strategy/start",
                          method="POST", strategy_instance_id=request.strategy_instance_id)
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/start")
            event = Event(EVENT_START_STRATEGY_INSTANCE, {
                "strategy_instance_id": request.strategy_instance_id,
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("start strategy request sent", request_id=request_id,
                          strategy_instance_id=request.strategy_instance_id)
            return await self.wait_for_response(future, request_id)

        @self.app.post("/api/strategy/stop", response_model=ApiResponseModel)
        async def stop_strategy_instance(request: StrategyInstanceIdRequest):
            flogger.debug("request received", endpoint="/api/strategy/stop",
                          method="POST", strategy_instance_id=request.strategy_instance_id)
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/stop")
            event = Event(EVENT_STOP_STRATEGY_INSTANCE, {
                "strategy_instance_id": request.strategy_instance_id,
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("stop strategy request sent", request_id=request_id,
                          strategy_instance_id=request.strategy_instance_id)
            return await self.wait_for_response(future, request_id)

        @self.app.post("/api/strategy/delete", response_model=ApiResponseModel)
        async def delete_strategy_instance(request: StrategyInstanceIdRequest):
            flogger.debug("request received", endpoint="/api/strategy/delete",
                          method="POST", strategy_instance_id=request.strategy_instance_id)
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/delete")
            event = Event(EVENT_DELETE_STRATEGY_INSTANCE, {
                "strategy_instance_id": request.strategy_instance_id,
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("delete strategy request sent", request_id=request_id,
                          strategy_instance_id=request.strategy_instance_id)
            return await self.wait_for_response(future, request_id)

        @self.app.post("/api/strategy/parameter_update", response_model=ApiResponseModel)
        async def update_strategy_instance_parameter(request: UpdateStrategyInstanceParamRequest):
            flogger.debug("request received", endpoint="/api/strategy/parameter_update", method="POST",
                          strategy_instance_id=request.strategy_instance_id, param_name=request.param_key, param_value=request.param_value)
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/parameter_update")
            event = Event(EVENT_UPDATE_STRATEGY_INSTANCE_PARAM, {
                "strategy_instance_id": request.strategy_instance_id,
                "param_name": request.param_key,
                "param_value": request.param_value,
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("update strategy parameter request sent", request_id=request_id,
                          strategy_instance_id=request.strategy_instance_id, param_name=request.param_key, param_value=request.param_value)
            return await self.wait_for_response(future, request_id)

        @self.app.post("/api/strategy/modify_instance_priority", response_model=ApiResponseModel)
        async def modify_instance_priority(request: ModifyInstancePriorityRequest):
            flogger.debug("request received", endpoint="/api/strategy/modify_instance_priority", method="POST",
                          strategy_instance_id=request.strategy_instance_id, priority=request.strategy_instance_priority,
                          strategy_instance_name=request.strategy_instance_name)
            request_id = self.response_manager.generate_request_id()
            future = self.response_manager.register_request(
                request_id, "/api/strategy/modify_instance_priority")
            event = Event(EVENT_MODIFY_INSTANCE_PRIORITY, {
                "strategy_instance_id": request.strategy_instance_id,
                "priority": request.strategy_instance_priority,
                "strategy_instance_name": request.strategy_instance_name,
                "request_id": request_id
            })
            self.event_engine.put(event)
            flogger.debug("modify instance priority request sent", request_id=request_id,
                          strategy_instance_id=request.strategy_instance_id, priority=request.strategy_instance_priority,
                          strategy_instance_name=request.strategy_instance_name)
            return await self.wait_for_response(future, request_id)

        @self.app.get("/api/health", response_model=ApiResponseModel)
        async def health_check():
            flogger.debug("request received",
                          endpoint="/api/health", method="GET")
            return ApiResponseModel(success=True, message="server is running")

        flogger.debug("routes setup completed")

    def start(self) -> None:
        flogger.info("starting http server")
        self.thread = threading.Thread(target=self._run, name="HttpServer")
        self.thread.daemon = True
        self.thread.start()
        flogger.info("http server started", host=self.host, port=self.port)

    def _run(self) -> None:
        """运行HTTP服务器"""
        flogger.debug("http server thread running")
        self.http_server_thread = threading.current_thread()

        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info",
            timeout_keep_alive=0,  # 0表示禁用超时，连接将保持打开状态直到客户端关闭
            backlog=2048
        )

    def stop(self) -> None:
        """停止HTTP服务器"""
        flogger.info("stopping http server")
        if self.thread and self.thread.is_alive():
            pass

        if hasattr(self, "response_queue"):
            try:
                self.response_queue.join()
                flogger.debug("response queue joined")
            except Exception as e:
                flogger.error("response queue join failed", error=str(e))
        flogger.info("http server stopped")

    async def send_response(self, request_id: int, success: bool, message: str, data: Optional[Dict[str, Any]] = None) -> None:
        try:
            if data is None:
                data = {}
            data["response_type"] = "processing_result"
            response = ApiResponse(success=success, message=message, data=data)
            await self.response_manager.send_response(request_id, response)
            flogger.info("response sent", request_id=request_id,
                         success=success, message=message)
        except Exception as e:
            flogger.error("response sent failed", error=str(e))

    def send_response_sync(self, request_id: int, success: bool, message: str, data: Optional[Dict[str, Any]] = None) -> None:
        try:
            self.response_queue.put((request_id, success, message, data))
            flogger.info("response queued", request_id=request_id,
                         success=success, message=message)
        except Exception as e:
            flogger.error("response queue put failed", error=str(e))

    async def _process_response_queue(self):
        flogger.debug("response queue processor started")
        while True:
            try:
                if not self.response_queue.empty():
                    request_id, success, message, data = self.response_queue.get_nowait()
                    await self.send_response(request_id, success, message, data)
                    self.response_queue.task_done()
                    flogger.debug("response processed", request_id=request_id)
                await asyncio.sleep(0.01)
            except asyncio.CancelledError:
                flogger.info("response queue processor cancelled")
                break
            except Exception as e:
                flogger.error("response processing failed", error=str(e))
                await asyncio.sleep(0.01)

    async def wait_for_response(self, future: asyncio.Future, request_id: int, timeout: float = 30.0) -> ApiResponseModel:
        try:
            flogger.debug("waiting for response",
                          request_id=request_id, timeout=timeout)
            response = await asyncio.wait_for(future, timeout=timeout)
            flogger.debug("response received", request_id=request_id)
            return ApiResponseModel(
                success=response.success,
                message=response.message,
                data=response.data,
                request_id=request_id
            )
        except asyncio.TimeoutError:
            flogger.error("response timeout",
                          request_id=request_id, timeout=timeout)
            return ApiResponseModel(success=False, message="request time out", data={"request_id": request_id}, request_id=request_id)
