import json
import time
import threading
import asyncio
from dataclasses import dataclass
from typing import Dict, Any, Optional, Callable, List
from utils.flog import flogger


@dataclass
class ApiResponse:
    """API响应基类"""
    success: bool
    message: str
    data: Optional[Any] = None
    request_id: Optional[int] = None

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "success": self.success,
            "message": self.message
        }
        if self.data is not None:
            result["data"] = self.data
        if self.request_id is not None:
            result["request_id"] = self.request_id
        return result

    def to_json(self) -> str:
        return json.dumps(self.to_dict())


class HttpResponseManager:
    """HTTP响应管理器，负责处理HTTP请求的响应"""

    def __init__(self):
        self.response_handlers: Dict[int, Dict] = {}
        self.next_request_id = 1
        self.lock = threading.Lock()
        self.response_futures: Dict[int, asyncio.Future] = {}
        self.response_results: Dict[int, ApiResponse] = {}

    def generate_request_id(self) -> int:
        """生成请求ID"""
        with self.lock:
            request_id = self.next_request_id
            self.next_request_id += 1
            flogger.debug("request id generated", request_id=request_id)
            return request_id

    def register_request(self, request_id: int, endpoint: str) -> asyncio.Future:
        """注册请求并返回一个 Future 对象，用于等待响应"""
        self.response_handlers[request_id] = {
            "endpoint": endpoint,
            "timestamp": time.time()
        }

        # 创建一个 Future 对象，用于等待响应
        loop = asyncio.get_event_loop()
        future = loop.create_future()
        self.response_futures[request_id] = future

        flogger.debug("request registered",
                      request_id=request_id, endpoint=endpoint)
        return future

    async def send_response(self, request_id: int, response: ApiResponse) -> bool:
        """发送响应

        如果有等待的Future，则设置结果
        """
        if request_id not in self.response_handlers:
            flogger.warning("request not found", request_id=request_id)
            return False

        self.response_handlers.pop(request_id)

        # 准备响应数据
        response.request_id = request_id
        if response.data is None:
            response.data = {}
        if isinstance(response.data, dict) and "response_type" not in response.data:
            response.data["response_type"] = "processing_result"

        # 如果有等待的Future，则设置结果
        if request_id in self.response_futures:
            future = self.response_futures.pop(request_id)
            if not future.done():
                future.set_result(response)
            self.response_results[request_id] = response
            flogger.info("response result set",
                         request_id=request_id, success=response.success)
            return True

        flogger.debug("no waiting future", request_id=request_id)
        return False
