from py_strategy_api.fb_enum import *
from .flog import flogger

valid_transitions = {
    StrategyStateEnum.STRATEGY_INIT_STAT.value: [
        StrategyStateEnum.STRATEGY_RUNNING_STAT.value,
        StrategyStateEnum.STRATEGY_DELETE_STAT.value,
        StrategyStateEnum.STRATEGY_PAUSE_STAT.value
    ],
    StrategyStateEnum.STRATEGY_RUNNING_STAT.value: [
        StrategyStateEnum.STRATEGY_PAUSE_STAT.value
    ],
    StrategyStateEnum.STRATEGY_PAUSE_STAT.value: [
        StrategyStateEnum.STRATEGY_RUNNING_STAT.value,
        StrategyStateEnum.STRATEGY_DELETE_STAT.value
    ],
    StrategyStateEnum.STRATEGY_TIMEOUT_STAT.value: [
        StrategyStateEnum.STRATEGY_PAUSE_STAT.value
    ]
}


def valid_status_transition(strategy_instance_id: int, current_status: str, new_status: str) -> bool:
    if current_status not in valid_transitions:
        flogger.error("invalid current status", strategy_instance_id=strategy_instance_id,
                      current_status=current_status, new_status=new_status)
        return False

    if new_status not in valid_transitions[current_status]:
        flogger.error("invalid status transition attempt", strategy_instance_id=strategy_instance_id,
                      current_status=current_status, new_status=new_status)
        return False

    flogger.debug("valid status transition", strategy_instance_id=strategy_instance_id,
                  current_status=current_status, new_status=new_status)

    return True
