import structlog
import os
from typing import <PERSON><PERSON>, Any
import logging
from structlog.typing import FilteringBoundLogger
from structlog.processors import CallsiteParameter

__all__ = ["FLOG_CRITICAL", "FLOG_FATAL", "FLOG_ERROR", "FLOG_WARN",
           "FLOG_INFO", "FLOG_DEBUG", "flogger", "flog_init", "FLOG"]

FLOG_CRITICAL = logging.CRITICAL
FLOG_FATAL = logging.FATAL
FLOG_ERROR = logging.ERROR
FLOG_WARN = logging.WARNING
FLOG_INFO = logging.INFO
FLOG_DEBUG = logging.DEBUG
FLOG_NOTSET = logging.NOTSET


flogger: FilteringBoundLogger = structlog.get_logger()


def flog_init(file_path: str, log_level: int = FLOG_INFO) -> None:
    """ init flog config, should be called only once

    Args:
        file_path (str): full log file path
        log_level (int, optional): Defaults to FLOG_INFO. when set to FLOG_DEBUG, will add module & lineno information to each line
    """
    __set_log_file(file_path)
    __set_log_level(log_level)
    structlog.configure(cache_logger_on_first_use=True)
    global flogger
    flogger = structlog.get_logger()


# compatiable with XLOG format
def FLOG(level: int, event: str, *args: Any, **kw: Any) -> Any:
    global flogger
    flogger.log(level, event, *args, **kw)


def __set_log_file(file_path: str):
    dir_path: str = os.path.abspath(os.path.dirname(file_path))
    if not os.path.isdir(dir_path):
        os.makedirs(dir_path)
    log_file: TextIO = open(file_path, "a")
    structlog.configure(
        logger_factory=structlog.PrintLoggerFactory(file=log_file))


def __set_log_level(log_level: int = FLOG_INFO):
    structlog.configure(
        wrapper_class=structlog.make_filtering_bound_logger(log_level))
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.CallsiteParameterAdder(
                parameters=[CallsiteParameter.MODULE, CallsiteParameter.LINENO]),
            structlog.processors.format_exc_info,
            structlog.processors.TimeStamper(fmt="iso", utc=False, key="ts"),
            structlog.processors.LogfmtRenderer(
                sort_keys=True, key_order=["ts", "level", "event", "module", "lineno"], bool_as_flag=False),
        ],
    )
