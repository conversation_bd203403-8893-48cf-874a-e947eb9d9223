import json
import os
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict, field, fields
from pathlib import Path
from utils.flog import flogger, flog_init


@dataclass
class FrontApiConfig:
    """Front API Configuration"""
    pub_address: str = "unix:////tmp/pengyc/MM_TOPIC_FRONT_SUMMARY_0_ALL"
    req_address: str = "unix:////tmp/pengyc/MM_TOPIC_FRONT_SERVICE_0_ALL"
    log_level: str = "info"
    log_dir: str = "./log/api.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class HttpServerConfig:
    """HTTP Server Configuration"""
    host: str = "0.0.0.0"
    port: int = 5000

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)



@dataclass
class LogConfig:
    """Log Configuration"""
    level: str = "info"
    file: str = "./log/py_fb_strategy.log"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class StrategyConfig:
    """Strategy Configuration"""
    py_strategy_plugin_dir: str = "./strategies"

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class AppConfig:
    """Application Configuration"""
    front_api: FrontApiConfig = field(default_factory=FrontApiConfig)
    http_server: HttpServerConfig = field(default_factory=HttpServerConfig)
    log: LogConfig = field(default_factory=LogConfig)
    strategy: StrategyConfig = field(default_factory=StrategyConfig)

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class Config:

    _instance = None

    @classmethod
    def get_instance(cls, config_file: str = None, node_id: int = None):
        if cls._instance is None:
            cls._instance = cls(config_file, node_id)
        return cls._instance

    def __init__(self, config_file: str = None, node_id: int = None):
        self.config_file = config_file
        self.node_id = node_id
        self.config = AppConfig()
        self.load()

    def load(self) -> None:
        """load config"""
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r") as f:
                    config_dict = json.load(f)
                self._update_config_from_dict(config_dict)
                print(
                    f"successfully loaded configuration from {self.config_file}")
            except Exception as e:
                print(
                    f"failed to load configuration from {self.config_file}: {e}")
                self.config = AppConfig()
                self.save()
        else:
            if self.config_file:
                print(
                    f"configuration file {self.config_file} dose not exist, using default configuration")
            else:
                print(f"no configuration file specified, using default configuration")
            self.config = AppConfig()
            self.save()
        self._configure_logging()
        flogger.info("init struct flogger")

    def _configure_logging(self):
        log_config = self.config.log
        log_level = log_config.level
        flog_init(log_config.file, log_level)

    def _update_config_from_dict(self, config_dict: Dict[str, Any]):

        node_config_key = f"py_strategy_engine {self.node_id}"

        if node_config_key not in config_dict:
            print(f"Configuration for node {self.node_id} (key: {node_config_key}) not found in config file")
            return

        node_config = config_dict[node_config_key]

        config_map = {
            "front_api": (FrontApiConfig, self.config.front_api),
            "http_server": (HttpServerConfig, self.config.http_server),
            "log": (LogConfig, self.config.log)
        }

        # 处理策略插件目录配置（直接从节点配置中读取）
        if "py_strategy_plugin_dir" in node_config:
            strategy_config = StrategyConfig(py_strategy_plugin_dir=node_config["py_strategy_plugin_dir"])
            self.config.strategy = strategy_config

        for config_key, (config_cls, current_config) in config_map.items():
            if config_key in node_config:
                config_data = node_config[config_key]
                update_config = self._update_dataclass_from_dict(
                    config_cls, current_config, config_data)
                setattr(self.config, config_key, update_config)

    def _update_dataclass_from_dict(self, dataclass_type, current_config_instance, update_dict: Dict[str, Any]):
        update_kwargs = {}
        for field in fields(dataclass_type):
            field_name = field.name
            if field_name in update_dict:
                update_kwargs[field_name] = update_dict[field_name]
            else:
                update_kwargs[field_name] = getattr(
                    current_config_instance, field_name)

        return dataclass_type(**update_kwargs)

    def save(self) -> None:
        if not self.config_file:
            print("no congiguration file path specified, cannot save configuration")
            return

        try:
            existing_config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, "r") as f:
                    existing_config = json.load(f)

            node_config_key = f"py_strategy_engine {self.node_id}"
            existing_config[node_config_key] = {
                "front_api": asdict(self.config.front_api),
                "http_server": asdict(self.config.http_server),
                "log": asdict(self.config.log),
                "py_strategy_plugin_dir": self.config.strategy.py_strategy_plugin_dir
            }

            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, "w") as f:
                json.dump(existing_config, f, indent=4)
        except Exception as e:
            print(f"failed to save configuration to  {self.config_file}: {e}")

    def get_front_api_config(self) -> FrontApiConfig:
        return self.config.front_api

    def get_http_server_config(self) -> HttpServerConfig:
        return self.config.http_server

    def get_log_config(self) -> LogConfig:
        return self.config.log

    def get_node_id(self) -> int:
        return self.node_id

    def get_strategy_config(self) -> StrategyConfig:
        return self.config.strategy

    def get_strategy_plugin_dir(self) -> str:
        return self.config.strategy.py_strategy_plugin_dir
