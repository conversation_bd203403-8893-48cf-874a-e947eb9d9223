{"py_strategy_engine 1": {"front_api": {"pub_address": "unix:////tmp/pengyc/MM_TOPIC_FRONT_SUMMARY_0_ALL", "req_address": "unix:////tmp/pengyc/MM_TOPIC_FRONT_SERVICE_0_ALL", "log_level": "debug", "log_dir": "./log/api_1.log"}, "http_server": {"host": "0.0.0.0", "port": 14300}, "log": {"level": "debug", "file": "./log/py_fb_strategy_1.log"}, "py_strategy_plugin_dir": "./strategies"}, "py_strategy_engine 2": {"front_api": {"pub_address": "unix:////tmp/pengyc/MM_TOPIC_FRONT_SUMMARY_1_ALL", "req_address": "unix:////tmp/pengyc/MM_TOPIC_FRONT_SERVICE_1_ALL", "log_level": "debug", "log_dir": "./log/api_2.log"}, "http_server": {"host": "0.0.0.0", "port": 14301}, "log": {"level": "debug", "file": "./log/py_fb_strategy_2.log"}, "py_strategy_plugin_dir": "./strategies"}, "py_strategy_engine 3": {"front_api": {"pub_address": "unix:////tmp/pengyc/MM_TOPIC_FRONT_SUMMARY_2_ALL", "req_address": "unix:////tmp/pengyc/MM_TOPIC_FRONT_SERVICE_2_ALL", "log_level": "debug", "log_dir": "./log/api_3.log"}, "http_server": {"host": "0.0.0.0", "port": 14302}, "log": {"level": "debug", "file": "./log/py_fb_strategy_3.log"}, "py_strategy_plugin_dir": "./strategies"}}