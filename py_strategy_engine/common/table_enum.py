from enum import Enum


class table_enum(Enum):
    """表格枚举"""
    exchange = 0X006F  # 产品信息
    product = 0X0008  # 产品信息
    option_serial = 0X0009  # 期权系列信息及基础属性
    security_instrument = 0X000C  # 现货合约
    option_instrument = 0X000B  # 期权合约
    future_instrument = 0X000A  # 期货合约信息及基础属性
    user = 0X0001  # 用户、用户角色基础信息
    market_data = 0X0015  # 原始市场行情
    order = 0X004E  # order指令流水
    trade = 0X004F  # 成交流水
    quote = 0X0050  # 报价流水
    position = 0X0039  # 合约持仓信息维护
    strategy_enum = 0X005A  # 策略枚举信息维护
    strategy_param = 0X005B  # 策略参数信息维护
    strategy_deploy = 0X005C  # 策略部署信息维护
    strategy_instance = 0X005D  # 策略实例信息维护
    strategy_instance_param = 0X005E  # 策略实例参数信息维护
    instrument_param_value = 0X0052  # 合约参数参数值
    custom_param_value = 0X0053  # 自定义参数参数值
    trading_account = 0X0054  # 交易账户


# 表格名称字典
table_name_dict: dict[str, table_enum] = {}
table_name_dict['exchange'] = table_enum.exchange
table_name_dict['product'] = table_enum.product
table_name_dict['option_serial'] = table_enum.option_serial
table_name_dict['option_instrument'] = table_enum.option_instrument
table_name_dict['future_instrument'] = table_enum.future_instrument
table_name_dict['security_instrument'] = table_enum.security_instrument
table_name_dict['user'] = table_enum.user
table_name_dict['market_data'] = table_enum.market_data
table_name_dict['order'] = table_enum.order
table_name_dict['trade'] = table_enum.trade
table_name_dict['quote'] = table_enum.quote
table_name_dict['position'] = table_enum.position
table_name_dict['strategy_enum'] = table_enum.strategy_enum
table_name_dict['strategy_deploy'] = table_enum.strategy_deploy
table_name_dict['strategy_param'] = table_enum.strategy_param
table_name_dict['strategy_instance'] = table_enum.strategy_instance
table_name_dict['strategy_instance_param'] = table_enum.strategy_instance_param
table_name_dict['instrument_param_value'] = table_enum.instrument_param_value
table_name_dict['custom_param_value'] = table_enum.custom_param_value
table_name_dict['trading_account'] = table_enum.trading_account


def get_table_enum(key: str) -> table_enum:
    """根据表格名称获取表格枚举"""
    return table_name_dict[key]
