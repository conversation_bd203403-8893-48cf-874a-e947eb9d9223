from typing import Dict, Any, Optional, List
from threading import Lock
from py_strategy_api.strategy_api import Strategy<PERSON><PERSON>
from utils.flog import flogger
from py_strategy_api.fb_enum import StrategyStateEnum
from common.models import *
from common.enum import *
from common.mdb import MemoryDatabase
from common.table_enum import *


class StrategyInstanceWrapper:
    def __init__(self, strategy_instance_id: int, strategy_api: StrategyApi, node_id: int):
        self.strategy_instance_id: int = strategy_instance_id
        self.strategy_api: StrategyApi = strategy_api
        self.node_id: int = node_id
        self.trading_account_id: int = None
        self.user_id: int = None
        self.status: str = None
        self.strategy_name: str = None
        self.strategy_instance_name: str = None
        self.strategy_version: str = self.strategy_api._api_version
        # 设置策略实例中的id
        self.set_strategy_instance_id(strategy_instance_id)

    def get_strategy_instance_api(self) -> StrategyApi:
        return self.strategy_api

    def get_trading_account_id(self) -> int:
        return self.trading_account_id

    def set_trading_account_id(self, trading_account_id: int) -> None:
        self.trading_account_id = trading_account_id

    def get_user_id(self) -> int:
        return self.user_id

    def set_user_id(self, user_id: int) -> None:
        self.user_id = user_id

    def get_strategy_instance_id(self) -> int:
        return self.strategy_instance_id

    def set_strategy_instance_id(self, strategy_instance_id: int) -> None:
        self.strategy_api._strategy_id = strategy_instance_id

    def get_strategy_name(self) -> str:
        return self.strategy_name

    def set_strategy_name(self, strategy_name: str) -> None:
        self.strategy_name = strategy_name

    def get_strategy_instance_name(self) -> str:
        return self.strategy_instance_name

    def set_strategy_instance_name(self, strategy_instance_name: str) -> None:
        self.strategy_instance_name = strategy_instance_name

    def get_strategy_instance_status(self) -> str:
        return self.status

    def set_strategy_instance_status(self, status: str) -> None:
        self.status = status

    def get_parameters(self) -> Dict[str, Any]:
        instance_params: Dict[str, Any] = {}
        if hasattr(self.strategy_api, 'instance_params') and self.strategy_api.instance_params:
            for name in self.strategy_api.instance_params:
                if hasattr(self.strategy_api, name):
                    value = getattr(self.strategy_api, name)
                    instance_params[name] = value
        return instance_params

    def update_parameter(self, param_name: str, param_value: Any) -> bool:
        """更新策略参数"""
        if hasattr(self.strategy_api, param_name):
            # 更新策略API实例中的参数
            setattr(self.strategy_api, param_name, param_value)
            return True
        else:
            flogger.error("parameter not exist in strategy instance",
                          strategy_instance_id=self.strategy_instance_id, param_name=param_name, param_value=param_value)
            return False


class StrategyInstanceManager:
    """策略实例管理器 - 单例模式，管理所有策略实例和信息"""

    _instance = None
    _lock = Lock()

    def __init__(self, node_id, mdb: MemoryDatabase):
        self.node_id: int = node_id
        self._instance_lock = Lock()
        self.mdb = mdb
        # 策略包装器字典 - 存储实际的策略实例
        self.strategy_instance_wrappers: Dict[int,
                                              StrategyInstanceWrapper] = {}
        flogger.info("strategy instance manager initialized", node_id=node_id)

    @classmethod
    def get_instance(cls, node_id: int, mdb: MemoryDatabase) -> 'StrategyInstanceManager':
        """获取单例实例"""
        flogger.info("get instance", node_id=node_id)
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls(node_id, mdb)
            return cls._instance

    def add_strategy_instance_wrapper(self, strategy_instance_id: int, strategy_name: str, strategy_instance_name: str, trading_account_id: int, user_id: int, strategy_api: StrategyApi) -> bool:
        """添加策略包装器"""
        with self._instance_lock:
            try:
                # 创建策略包装器
                wrapper = StrategyInstanceWrapper(
                    strategy_instance_id=strategy_instance_id,
                    strategy_api=strategy_api,
                    node_id=self.node_id
                )
                wrapper.set_trading_account_id(trading_account_id)
                wrapper.set_user_id(user_id)
                wrapper.set_strategy_name(strategy_name)
                wrapper.set_strategy_instance_name(strategy_instance_name)
                wrapper.set_strategy_instance_status(
                    StrategyStateEnum.STRATEGY_INIT_STAT.value)

                # 存储策略包装器
                self.strategy_instance_wrappers[strategy_instance_id] = wrapper

                self._update_strategy_instance_mdb(strategy_instance_id)
                self._init_strategy_instance_params_to_mdb(
                    strategy_instance_id, wrapper)

                flogger.info("strategy instance wrapper added",
                             strategy_instance_id=strategy_instance_id,
                             strategy_name=strategy_name,
                             strategy_instance_name=strategy_instance_name,
                             trading_account_id=wrapper.get_trading_account_id(),
                             status=StrategyStateEnum.STRATEGY_INIT_STAT.value,
                             node_id=self.node_id)
                return True

            except Exception as e:
                flogger.error("failed to add strategy wrapper",
                              strategy_instance_id=strategy_instance_id, error=str(e))
                return False

    def check_strategy_instance_exists(self, strategy_instance_id) -> bool:
        """检查策略实例是否存在"""
        return strategy_instance_id in self.strategy_instance_wrappers

    def get_strategy_instance_name(self, strategy_instance_id: int) -> Optional[str]:
        """获取策略实例名称"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_instance_name()

    def get_strategy_name(self, strategy_instance_id: int) -> Optional[str]:
        """获取策略名称"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_name()

    def get_strategy_instance_id_list(self) -> List[int]:
        """获取策略实例id列表"""
        return list(self.strategy_instance_wrappers.keys())

    def get_strategy_instance_wrapper(self, strategy_instance_id: int) -> Optional[StrategyInstanceWrapper]:
        """获取策略包装器"""
        return self.strategy_instance_wrappers.get(strategy_instance_id)

    def remove_strategy_instance_wrapper(self, strategy_instance_id: int) -> bool:
        """移除策略包装器"""
        with self._instance_lock:
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if not wrapper:
                flogger.warning("strategy instance wrapper not found",
                                strategy_instance_id=strategy_instance_id)
                return False
            try:
                wrapper.set_strategy_instance_status(
                    StrategyStateEnum.STRATEGY_DELETE_STAT.value)
                self._update_strategy_instance_mdb(strategy_instance_id)
                del self.strategy_instance_wrappers[strategy_instance_id]
                flogger.info("strategy instance wrapper removed",
                             strategy_instance_id=strategy_instance_id)
                return True
            except Exception as e:
                flogger.error("failed to remove strategy instance wrapper",
                              strategy_instance_id=strategy_instance_id, error=str(e))
                return False

    def get_strategy_instance_api(self, strategy_instance_id: int) -> Optional[StrategyApi]:
        """获取策略实例API"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_instance_api()

    def get_strategy_instance_trading_account_id(self, strategy_instance_id: int) -> Optional[int]:
        """获取策略的交易账户ID"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_trading_account_id()

    def get_strategy_instance_user_id(self, strategy_instance_id: int) -> Optional[int]:
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_user_id()

    def get_strategy_instance_status(self, strategy_instance_id: int) -> str:
        """获取策略实例状态"""
        wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
        if not wrapper:
            flogger.warning("strategy instance wrapper not found",
                            strategy_instance_id=strategy_instance_id)
            return None
        return wrapper.get_strategy_instance_status()

    # ========== 策略信息管理功能 ==========

    def update_strategy_status(self, strategy_instance_id: int, status: str) -> bool:
        """更新策略状态"""
        flogger.info("update strategy instance status",
                     strategy_instance_id=strategy_instance_id, status=status)
        with self._instance_lock:
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if not wrapper:
                flogger.warning("strategy instance wrapper not found",
                                strategy_instance_id=strategy_instance_id)
                return False
            try:
                wrapper.set_strategy_instance_status(status)
                self._update_strategy_instance_mdb(strategy_instance_id)
            except Exception as e:
                flogger.error("update strategy status failed",
                              strategy_instance_id=strategy_instance_id, status=status, error=str(e))
            return True

    def update_strategy_parameter(self, strategy_instance_id: int, param_name: str, param_value: Any) -> bool:
        """更新策略参数"""
        with self._instance_lock:
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if wrapper:
                try:
                    wrapper.update_parameter(param_name, param_value)
                    self._update_strategy_instance_param_mdb(
                        strategy_instance_id, param_name, param_value)
                except Exception as e:
                    flogger.warning("update parameter failed",
                                    strategy_instance_id=strategy_instance_id, param_name=param_name, error=str(e))
                    return False
            else:
                flogger.warning("parameter not found in strategy instance",
                                strategy_instance_id=strategy_instance_id, param_name=param_name)
                return False

            flogger.info("update strategy instance parameter", strategy_instance_id=strategy_instance_id,
                         param_name=param_name, param_value=param_value)
            return True

    def get_strategy_instance_info(self, strategy_instance_id: int) -> Optional[StrategyInstanceMsg]:
        """获取策略信息"""
        flogger.info("get strategy instance info",
                     strategy_instance_id=strategy_instance_id)
        record = self.mdb.get_records_by_params(
            table_enum.strategy_instance, strategy_instance_id=strategy_instance_id)
        return record

    def get_all_strategy_instance_infos(self) -> List[StrategyInstanceMsg]:
        """获取所有未删除策略实例信息"""
        flogger.info("get all strategy instances info")
        all_records: List[StrategyInstanceMsg] = self.mdb.get_all_records(
            table_enum.strategy_instance)
        if not all_records:
            flogger.debug("no strategy instance found")
            return []
        active_records: List[StrategyInstanceMsg] = []
        for record in all_records:
            # mdb储存的是枚举对象
            if record.strategy_instance_state != StrategyStateEnum.STRATEGY_DELETE_STAT:
                active_records.append(record)
        return active_records

    def get_all_strategy_instance_infos_include_delete(self) -> List[StrategyInstanceMsg]:
        """获取所有策略实例信息"""
        flogger.info("get all strategy instance infos include delete")
        records: List[StrategyInstanceMsg] = self.mdb.get_all_records(
            table_enum.strategy_instance)
        return records if records else []

    def get_strategy_instance_parameters(self, strategy_instance_id: int) -> List[StrategyInstanceParamMsg]:
        """获取策略参数"""
        flogger.info("get strategy instance parameters",
                     strategy_instance_id=strategy_instance_id)

        all_params: List[StrategyInstanceParamMsg] = self.mdb.get_all_records(
            table_enum.strategy_instance_param)
        if not all_params:
            flogger.debug("no strategy instance parameters found in mdb")
            return []
        # 过滤出指定实例的参数
        instance_params = []
        for param in all_params:
            if param.strategy_instance_id == strategy_instance_id:
                instance_params.append(param)
        flogger.debug("found strategy instance parameters",
                      strategy_instance_id=strategy_instance_id,
                      param_count=len(instance_params))
        return instance_params

    def get_strategy_instance_parameter(self, strategy_instance_id: int, param_key: str) -> Optional[StrategyInstanceParamMsg]:
        """获取单个策略实例参数 - 从MDB查询"""
        flogger.debug("get strategy instance parameter",
                      strategy_instance_id=strategy_instance_id, param_key=param_key)

        # 从MDB查询指定参数
        param: StrategyInstanceParamMsg = self.mdb.get_records_by_params(
            table_enum.strategy_instance_param,
            strategy_instance_id=strategy_instance_id,
            param_key=param_key
        )

        if param:
            flogger.debug("found strategy instance parameter",
                          strategy_instance_id=strategy_instance_id,
                          param_key=param_key,
                          param_value=param.param_value)
        else:
            flogger.debug("strategy instance parameter not found",
                          strategy_instance_id=strategy_instance_id,
                          param_key=param_key)

        return param

    def get_all_strategy_instance_parameters(self) -> List[StrategyInstanceParamMsg]:
        """获取所有策略实例参数"""
        flogger.info("get all strategy instance parameters")

        all_params: List[StrategyInstanceParamMsg] = self.mdb.get_all_records(
            table_enum.strategy_instance_param)
        if not all_params:
            flogger.debug("no strategy instance parameters found in mdb")
            return []

        flogger.debug("found all strategy instance parameters",
                      param_count=len(all_params))
        return all_params

    def delete_strategy_instance(self, strategy_instance_id: int) -> bool:
        """删除策略实例"""
        return self.remove_strategy_instance_wrapper(strategy_instance_id)

    def get_strategy_instance_count(self) -> int:
        """获取策略总数"""
        return len(self.strategy_instance_wrappers)

    def get_strategies_by_status(self, status: str) -> List[int]:
        """根据状态获取策略列表"""
        return [wrapper.get_strategy_instance_id() for wrapper in self.strategy_instance_wrappers.values() if wrapper.get_strategy_instance_status() == status]

    def get_strategies_by_account(self, trading_account_id: int) -> List[int]:
        """根据交易账户ID获取策略列表"""
        return [wrapper.get_strategy_instance_id() for wrapper in self.strategy_instance_wrappers.values()
                if wrapper.get_trading_account_id() == trading_account_id]

    def modify_instance_priority(self, strategy_instance_id: int, priority: int, strategy_instance_name: str) -> bool:
        """修改策略实例优先级"""
        flogger.info("modify strategy instance priority",
                     strategy_instance_id=strategy_instance_id,
                     priority=priority,
                     strategy_instance_name=strategy_instance_name)

        with self._instance_lock:
            # 检查wrapper是否存在
            wrapper = self.strategy_instance_wrappers.get(strategy_instance_id)
            if not wrapper:
                flogger.warning("strategy instance wrapper not found",
                                strategy_instance_id=strategy_instance_id)
                return False

            try:
                strategy_instance_msg: StrategyInstanceMsg = self.mdb.get_records_by_params(
                    table_enum.strategy_instance, strategy_instance_id=strategy_instance_id)

                if strategy_instance_msg:
                    strategy_instance_msg.strategy_instance_priority = 0
                    strategy_instance_msg.strategy_instance_name = strategy_instance_name

                    self.mdb.update_record(
                        table_enum.strategy_instance, strategy_instance_msg)

                    flogger.info("strategy instance priority updated successfully",
                                 strategy_instance_id=strategy_instance_id,
                                 priority=priority,
                                 strategy_instance_name=strategy_instance_name)
                    return True
                else:
                    flogger.error("strategy instance record not found in MDB",
                                  strategy_instance_id=strategy_instance_id)
                    return False

            except Exception as e:
                flogger.error("failed to modify strategy instance priority",
                              strategy_instance_id=strategy_instance_id,
                              priority=priority,
                              error=str(e))
                return False

    def _update_strategy_instance_param_mdb(self, strategy_instance_id: int, param_key: str, param_value: Any) -> None:
        """更新策略实例参数到MDB"""
        try:
            # 创建策略实例参数消息
            param_msg = StrategyInstanceParamMsg(
                strategy_instance_id=strategy_instance_id,
                strategy_engine_id=self.node_id,
                param_key=param_key,
                param_value=str(param_value),
                last_operator_id=0
            )

            flogger.info("update strategy instance param mdb",
                         strategy_instance_id=strategy_instance_id,
                         param_key=param_key,
                         param_value=param_value,
                         data=param_msg)
            self.mdb.update_record(
                table_enum.strategy_instance_param, param_msg)

        except Exception as e:
            flogger.error("update strategy instance param mdb failed",
                          strategy_instance_id=strategy_instance_id,
                          param_key=param_key,
                          param_value=param_value,
                          error=str(e))

    def _init_strategy_instance_params_to_mdb(self, strategy_instance_id: int, wrapper: StrategyInstanceWrapper) -> None:
        """初始化策略实例参数到MDB"""
        try:
            parameters = wrapper.get_parameters()
            if not parameters:
                flogger.debug("no parameters found in wrapper",
                              strategy_instance_id=strategy_instance_id)
                return

            for param_name, param_value in parameters.items():
                param_msg = StrategyInstanceParamMsg(
                    strategy_instance_id=strategy_instance_id,
                    strategy_engine_id=self.node_id,
                    param_key=param_name,
                    param_value=str(param_value),
                    last_operator_id=0
                )

                self.mdb.update_record(
                    table_enum.strategy_instance_param, param_msg)
                flogger.debug("init strategy instance param to mdb",
                              strategy_instance_id=strategy_instance_id,
                              param_key=param_name,
                              param_value=param_value)

            flogger.info("init strategy instance params to mdb completed",
                         strategy_instance_id=strategy_instance_id,
                         param_count=len(parameters))
        except Exception as e:
            flogger.error("init strategy instance params to mdb failed",
                          strategy_instance_id=strategy_instance_id,
                          error=str(e))

    def _update_strategy_instance_mdb(self, strategy_instance_id: int) -> None:
        instance_wrapper: StrategyInstanceWrapper = self.strategy_instance_wrappers.get(
            strategy_instance_id)
        if not instance_wrapper:
            flogger.error("strategy instance wrapper not found",
                          strategy_instance_id=strategy_instance_id)
            return
        strategy_instance_msg = None
        try:
            strategy_instance_msg = StrategyInstanceMsg(
                strategy_instance_id=strategy_instance_id,
                strategy_name=self.get_strategy_name(strategy_instance_id),
                strategy_engine_id=self.node_id,
                strategy_instance_name=self.get_strategy_instance_name(
                    strategy_instance_id),
                user_id=self.get_strategy_instance_user_id(
                    strategy_instance_id),
                strategy_instance_state=self.get_strategy_instance_status(
                    strategy_instance_id),
                trading_account_id=self.get_strategy_instance_trading_account_id(
                    strategy_instance_id),
                strategy_instance_priority=0,
                last_operator_id=0,
                strategy_pause_reason=StrategyPauseReasonEnum.PAUSE_BY_USER_OPERATOR.value
            )
            flogger.info("update strategy instance mdb",
                         strategy_instance_id=strategy_instance_id, data=strategy_instance_msg)
            self.mdb.update_record(
                table_enum.strategy_instance, strategy_instance_msg)
        except Exception as e:
            flogger.error("update mdb record failed", table=table_enum.strategy_instance,
                          strategy_instance_id=strategy_instance_id, data=strategy_instance_msg if strategy_instance_msg else None, error=str(e))
