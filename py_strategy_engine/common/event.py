from queue import Queue, Empty
from threading import Thread
from typing import Dict, List, Callable, Any
from dataclasses import dataclass
from common.constants import *
from utils.flog import flogger


@dataclass
class Event:
    type: str
    data: Any = None


class EventEngine:
    """事件引擎，负责事件的分发和处理"""

    def __init__(self):
        flogger.info("event engine start init")
        self._queue = Queue()
        self._active = False
        self._thread = Thread(target=self._run, name="EventEngine")
        self._handlers: Dict[str, List[Callable]] = {}
        self._general_handlers: List[Callable] = []
        flogger.info("event engine end init")

    def _run(self):
        """事件处理线程"""
        flogger.debug("event processing thread started")
        while self._active:
            try:
                event = self._queue.get(block=True, timeout=1)

                # 检查停止信号
                if event.type == "__STOP__":
                    flogger.debug("received stop signal")
                    break

                flogger.debug("event received", event_type=event.type)

                # 处理事件，添加异常保护
                try:
                    self._process(event)
                except Exception as e:
                    flogger.error("event processing error", event_type=event.type, error=str(e))

            except Empty:
                # 超时，继续循环检查 _active 状态
                continue
            except Exception as e:
                flogger.error("event thread error", error=str(e))

        # 线程结束时打印队列状态用于调试
        final_queue_size = self._queue.qsize()
        flogger.info("event processing thread stopped",
                    final_queue_size=final_queue_size,
                    active_status=self._active)

        # 如果队列还有事件，打印前几个事件类型用于调试
        if final_queue_size > 0:
            event_types = []
            temp_events = []
            sample_count = min(10, final_queue_size)  # 最多采样10个事件

            for i in range(sample_count):
                try:
                    event = self._queue.get_nowait()
                    event_types.append(event.type)
                    temp_events.append(event)
                except:
                    break

            # 将采样的事件放回队列
            for event in temp_events:
                try:
                    self._queue.put_nowait(event)
                except:
                    break

            flogger.warning("thread stopped with events remaining in queue",
                          remaining_events=final_queue_size,
                          sample_event_types=event_types)

    def _process(self, event: Event):
        """处理事件"""
        if event.type in self._handlers:
            flogger.debug("processing event", event_type=event.type)
            [handler(event) for handler in self._handlers[event.type]]

        if self._general_handlers:
            flogger.debug("processing general event", event_type=event.type)
            [handler(event) for handler in self._general_handlers]



    def start(self):
        """启动事件引擎"""
        flogger.debug("starting event engine", active_status=self._active)
        self._active = True
        self._thread.start()
        flogger.debug("event engine started", active_status=self._active)

    def stop(self):
        """停止事件引擎"""
        flogger.debug("stopping event engine", active_status=self._active)
        self._active = False

        # 向队列发送停止信号，唤醒可能正在等待的事件处理线程
        try:
            self._queue.put(Event(type="__STOP__"), timeout=1)
        except:
            pass  # 如果队列满了，忽略错误

        # 等待事件处理线程停止，添加超时
        if self._thread.is_alive():
            pre_join_queue_size = self._queue.qsize()
            flogger.info("waiting for event thread to stop",
                        queue_size=pre_join_queue_size,
                        thread_name=self._thread.name)

            self._thread.join(timeout=5)

            if self._thread.is_alive():
                timeout_queue_size = self._queue.qsize()

                # 采样队列中的事件类型用于调试
                event_type_counts = {}
                temp_events = []
                sample_size = min(20, timeout_queue_size)  # 采样20个事件

                for _ in range(sample_size):
                    try:
                        event = self._queue.get_nowait()
                        event_type = event.type
                        event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
                        temp_events.append(event)
                    except:
                        break

                # 将采样的事件放回队列
                for event in temp_events:
                    try:
                        self._queue.put_nowait(event)
                    except:
                        break

                flogger.warning("event processing thread did not stop within timeout",
                              thread_name=self._thread.name,
                              queue_size_before_join=pre_join_queue_size,
                              queue_size_after_timeout=timeout_queue_size,
                              sampled_event_types=event_type_counts,
                              handlers_registered=list(self._handlers.keys()))

                # 强制设置线程为非daemon，让程序能够退出
                # 注意：线程可能仍在后台运行，但不会阻止程序退出
                try:
                    self._thread.daemon = True
                    flogger.warning("set event thread as daemon to allow program exit")
                except Exception as e:
                    flogger.error("failed to set thread as daemon", error=str(e))

        final_queue_size = self._queue.qsize()
        flogger.info("event engine stopped",
                    active_status=self._active,
                    final_queue_size=final_queue_size,
                    thread_alive=self._thread.is_alive())

        # 如果停止后队列还有事件，记录详细信息
        if final_queue_size > 0:
            flogger.warning("event engine stopped but queue not empty",
                          remaining_events=final_queue_size)

    def put(self, event: Event):
        """放入事件"""
        if not self._active and event.type != "__STOP__":
            # 如果引擎已停止，不接受新事件（除了停止信号）
            return

        if event.type != "__STOP__":
            flogger.debug("put event", event_type=event.type)

        try:
            self._queue.put(event, timeout=1)  # 添加超时，避免阻塞
        except Exception as e:
            flogger.error("put event failed", event_type=event.type, error=str(e))

    def register(self, event_type: str, handler: Callable):
        """注册事件处理函数"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
            flogger.debug("create event handler", event_type=event_type)

        if handler not in self._handlers[event_type]:
            self._handlers[event_type].append(handler)
            flogger.debug("register handler", event_type=event_type)

    def unregister(self, event_type: str, handler: Callable):
        """注销事件处理函数"""
        if event_type in self._handlers:
            if handler in self._handlers[event_type]:
                self._handlers[event_type].remove(handler)
                flogger.debug("unregister handler", event_type=event_type)

    def register_general(self, handler: Callable):
        """注册通用事件处理函数"""
        if handler not in self._general_handlers:
            self._general_handlers.append(handler)
            flogger.debug("register general handler")

    def unregister_general(self, handler: Callable):
        """注销通用事件处理函数"""
        if handler in self._general_handlers:
            self._general_handlers.remove(handler)
            flogger.debug("unregister general handler")
