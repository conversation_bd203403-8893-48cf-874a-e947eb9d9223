from typing import Dict, List, Optional, Type, Any
from threading import Lock
from utils.flog import flogger
from common.models import StrategyDeployMsg, StrategyParamMsg
from common.enum import IntStatusEnum
from common.mdb import MemoryDatabase
from common.table_enum import table_enum
from py_strategy_api.strategy_api import StrategyApi


class StrategyManager:
    """策略部署管理器"""

    _instance = None
    _lock = Lock()

    def __init__(self, engine_id: int, mdb: MemoryDatabase):
        self.engine_id = engine_id
        self.mdb = mdb
        self._lock = Lock()
        self.strategy_classes: Dict[str, Type[StrategyApi]] = {}
        flogger.info("strategy manager initialized", engine_id=engine_id)

    @classmethod
    def get_instance(cls, engine_id: int = None, mdb: MemoryDatabase = None) -> 'StrategyManager':
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    if engine_id is None or mdb is None:
                        raise ValueError(
                            "engine_id and mdb are required for first initialization")
                    cls._instance = cls(engine_id, mdb)
        return cls._instance

    def get_strategy_version_from_class(self, strategy_name: str) -> str:
        """从策略类获取版本号"""
        strategy_class = self.strategy_classes.get(strategy_name)
        if strategy_class:
            # 优先从类属性获取版本
            if hasattr(strategy_class, 'strategy_version'):
                return strategy_class.strategy_version
            elif hasattr(strategy_class, '_api_version'):
                return strategy_class._api_version
        return ""

    def add_or_update_strategy_deploy(self, strategy_name: str, strategy_class: Type[StrategyApi]) -> bool:
        """添加或更新策略部署信息"""
        try:
            with self._lock:
                self.strategy_classes[strategy_name] = strategy_class
                self._analyze_and_store_strategy_params(
                    strategy_name, strategy_class)

                # 从策略类获取版本号
                strategy_version = self.get_strategy_version_from_class(
                    strategy_name)

                deploy_msg = StrategyDeployMsg(
                    strategy_name=strategy_name,
                    strategy_engine_id=self.engine_id,
                    strategy_version=strategy_version,
                    status=IntStatusEnum.ACTIVE.value,
                    last_operator_id=0
                )

                # 添加到内存数据库
                self.mdb.add_record(table_enum.strategy_deploy, deploy_msg)
                flogger.info("strategy deploy info added",
                             strategy_name=strategy_name,
                             strategy_version=strategy_version,
                             engine_id=self.engine_id,
                             last_operator_id=0)

                return True

        except Exception as e:
            flogger.error("failed to add/update strategy deploy info",
                          strategy_name=strategy_name,
                          error=str(e))
            return False

    def get_all_deployed_strategies(self) -> List[StrategyDeployMsg]:
        """获取所有已部署的策略信息"""
        try:
            with self._lock:
                # 从内存数据库获取所有策略部署记录
                all_records: List[StrategyDeployMsg] = self.mdb.get_all_records(
                    table_enum.strategy_deploy)
                if not all_records:
                    flogger.debug("no deployed strategies found")
                    return []
                # 过滤出有效状态的记录
                active_records: List[StrategyDeployMsg] = [
                    record for record in all_records
                    if record.status == IntStatusEnum.ACTIVE
                ]

                flogger.debug("retrieved deployed strategies",
                              total_count=len(all_records),
                              active_count=len(active_records))

                return active_records

        except Exception as e:
            flogger.error("failed to get deployed strategies", error=str(e))
            return []

    def get_strategy_deploy_by_name(self, strategy_name: str) -> Optional[StrategyDeployMsg]:
        """根据策略名获取部署信息"""
        try:
            with self._lock:
                return self._get_strategy_deploy_by_name(strategy_name)
        except Exception as e:
            flogger.error("failed to get strategy deploy by name",
                          strategy_name=strategy_name, error=str(e))
            return None

    def _get_strategy_deploy_by_name(self, strategy_name: str) -> Optional[StrategyDeployMsg]:
        """内部方法：根据策略名获取部署信息（不加锁）"""
        all_records = self.mdb.get_all_records(table_enum.strategy_deploy)

        for record in all_records:
            if (isinstance(record, StrategyDeployMsg) and
                record.strategy_name == strategy_name and
                record.strategy_engine_id == self.engine_id and  # 添加引擎ID检查
                    record.status == IntStatusEnum.ACTIVE):
                return record

        return None

    def deactivate_strategy_deploy(self, strategy_name: str, last_operator_id: int = 0) -> bool:
        """停用策略部署信息（设置为无效状态）"""
        try:
            with self._lock:
                existing_record = self._get_strategy_deploy_by_name(
                    strategy_name)

                if existing_record:
                    existing_record.status = IntStatusEnum.INACTIVE
                    existing_record.last_operator_id = last_operator_id

                    # 更新到内存数据库
                    self.mdb.update_record(
                        table_enum.strategy_deploy, existing_record)
                    flogger.info("strategy deploy info deactivated",
                                 strategy_name=strategy_name,
                                 last_operator_id=last_operator_id)
                    return True
                else:
                    flogger.warning("strategy deploy info not found for deactivation",
                                    strategy_name=strategy_name)
                    return False

        except Exception as e:
            flogger.error("failed to deactivate strategy deploy info",
                          strategy_name=strategy_name, error=str(e))
            return False

    def _analyze_and_store_strategy_params(self, strategy_name: str, strategy_class: Type[StrategyApi]) -> None:
        """分析策略类参数并存储到MDB"""
        try:
            # 检查策略类是否有parameters属性
            if not hasattr(strategy_class, 'instance_params') or not strategy_class.instance_params:
                flogger.debug("strategy class has no instance params",
                              strategy_name=strategy_name)
                return

            parameters_list = strategy_class.instance_params
            flogger.debug("analyzing strategy instance params",
                          strategy_name=strategy_name,
                          parameters=parameters_list)

            for index, param_name in enumerate(parameters_list):
                # 检查策略类是否有该属性
                if hasattr(strategy_class, param_name):
                    param_value = getattr(strategy_class, param_name)
                    param_type = self._get_param_type(param_value)

                    # 创建策略参数消息
                    param_msg = StrategyParamMsg(
                        strategy_name=strategy_name,
                        param_key=param_name,
                        param_type=param_type,
                        param_value=str(param_value),
                        index=index,
                        tick=0.0,  # 默认最小变动单位
                        status=IntStatusEnum.ACTIVE.value
                    )

                    # 存储到MDB
                    self.mdb.update_record(
                        table_enum.strategy_param, param_msg)
                    flogger.debug("strategy parameter stored",
                                  strategy_name=strategy_name,
                                  param_name=param_name,
                                  param_type=param_type,
                                  param_value=param_value)
                else:
                    flogger.warning("parameter not found in strategy class",
                                    strategy_name=strategy_name,
                                    param_name=param_name)

            flogger.info("strategy parameters analysis completed",
                         strategy_name=strategy_name,
                         param_count=len(parameters_list))

        except Exception as e:
            flogger.error("failed to analyze strategy parameters",
                          strategy_name=strategy_name,
                          error=str(e))

    def _get_param_type(self, param_value: Any) -> str:
        """根据参数值判断参数类型"""
        match param_value:
            case bool():
                return "bool"
            case int():
                return "int"
            case float():
                return "double"
            case str():
                return "string"
            case list():
                return "list"
            case dict():
                return "dict"
            case _:
                return "unknown"

    def get_strategy_parameters(self, strategy_name: str) -> List[StrategyParamMsg]:
        """获取策略参数列表"""
        try:
            with self._lock:
                all_params: List[StrategyParamMsg] = self.mdb.get_all_records(
                    table_enum.strategy_param)
                if not all_params:
                    flogger.debug("no strategy parameters found in mdb")
                    return []

                # 过滤出指定策略的参数
                strategy_params: List[StrategyParamMsg] = []
                for param in all_params:
                    if param.strategy_name == strategy_name and param.status == IntStatusEnum.ACTIVE:
                        strategy_params.append(param)

                # 按index排序
                strategy_params.sort(key=lambda x: x.index)

                flogger.debug("found strategy parameters",
                              strategy_name=strategy_name,
                              param_count=len(strategy_params))
                return strategy_params

        except Exception as e:
            flogger.error("failed to get strategy parameters",
                          strategy_name=strategy_name, error=str(e))
            return []

    def get_strategy_parameter(self, strategy_name: str, param_key: str) -> Optional[StrategyParamMsg]:
        """获取单个策略参数"""
        try:
            with self._lock:
                param: StrategyParamMsg = self.mdb.get_records_by_params(
                    table_enum.strategy_param,
                    strategy_name=strategy_name,
                    param_key=param_key
                )

                if param and param.status == IntStatusEnum.ACTIVE:
                    flogger.debug("found strategy parameter",
                                  strategy_name=strategy_name,
                                  param_key=param_key,
                                  param_value=param.param_value)
                    return param
                else:
                    flogger.debug("strategy parameter not found",
                                  strategy_name=strategy_name,
                                  param_key=param_key)
                    return None

        except Exception as e:
            flogger.error("failed to get strategy parameter",
                          strategy_name=strategy_name,
                          param_key=param_key,
                          error=str(e))
            return None

    def get_all_strategy_parameters(self) -> List[StrategyParamMsg]:
        """获取所有策略参数列表"""
        try:
            with self._lock:
                all_params: List[StrategyParamMsg] = self.mdb.get_all_records(
                    table_enum.strategy_param)
                if not all_params:
                    flogger.debug("no strategy parameters found in mdb")
                    return []

                # 过滤出有效状态的参数
                # active_params: List[StrategyParamMsg] = []
                # for param in all_params:
                #     if param.status == IntStatusEnum.ACTIVE:
                #         active_params.append(param)

                flogger.debug("found all strategy parameters",
                              total_count=len(all_params))
                # return active_params
                return all_params

        except Exception as e:
            flogger.error(
                "failed to get all strategy parameters", error=str(e))
            return []
