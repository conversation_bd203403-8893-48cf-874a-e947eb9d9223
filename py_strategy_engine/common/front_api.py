from typing import Dict, Any, Optional, List, Union
from pyfbapi_gateway.front_gateway import front_gateway
from pyfbapi_gateway.models import *
from common.event import Event, EventEngine
from common.constants import *
from common.table_enum import table_enum, get_table_enum
from utils.flog import flogger


class FrontApi:
    """Front API接口，负责与交易系统通信"""

    def __init__(self, event_engine: EventEngine):
        flogger.info("init front api")
        self.event_engine = event_engine
        flogger.debug("creating front gateway instance")
        self.gateway = front_gateway(self)
        self.connected = False
        flogger.info("front api init successfully")

    def connect(self, pub_address: str, req_address: str, log_level: str, log_dir: str):
        """连接Front API"""
        flogger.info("connecting to front api", pub_address=pub_address,
                     req_address=req_address, log_level=log_level)
        try:
            self.gateway.connect(pub_address, req_address, log_level, log_dir)
            self.connected = True
            flogger.info("front api connected successfully",
                         pub_address=pub_address, req_address=req_address)
        except Exception as e:
            flogger.error("front api connection failed",
                          pub_address=pub_address, req_address=req_address, error=str(e))

    def stop(self):
        """停止Front API"""
        if not self.connected:
            flogger.info("front api already stopped")
            return

        flogger.info("stopping front api")
        try:
            # 首先调用close停止gateway
            flogger.info("closing front api gateway")
            self.gateway.close()
            flogger.info("front api gateway closed")

            # 然后调用join等待线程结束
            flogger.info("joining front api gateway thread")
            self.gateway.join()
            flogger.info("front api gateway thread joined")

            self.connected = False
            flogger.info("front api stopped successfully")
        except Exception as e:
            flogger.error("front api stop failed", error=str(e))
            # 即使停止失败，也标记为未连接状态
            self.connected = False

    def sub(self, table: Union[str, table_enum], table_interval=100):
        flogger.debug("sub table", table=str(table), interval=table_interval)
        try:
            # 如果是表格枚举，转换为表格名称
            if isinstance(table, table_enum):
                table_name = table.name
            else:
                table_name = table
            self.gateway.register_table(table_name, table_interval)
            flogger.info("sub table success", table=table_name,
                         interval=table_interval)
        except Exception as e:
            flogger.error("sub table failed", table=table_name,
                          interval=table_interval, error=str(e))

    def create_order(self, order: OrderEntity, request_id: int):
        """发送订单"""
        flogger.debug("creating order via front api",
                      request_id=request_id, order_id=order.order_id)
        try:
            self.gateway.do_insert_order(order, request_id)
            flogger.info("order creation success",
                         request_id=request_id, order_id=order.order_id)
            return True
        except Exception as e:
            flogger.error("order creation failed", request_id=request_id,
                          order_id=order.order_id, error=str(e))
            return False

    def cancel_order(self, order: OrderEntity, request_id: int):
        """撤销订单"""
        flogger.debug("canceling order via front api",
                      request_id=request_id, order_id=order.order_id)
        try:
            self.gateway.do_cancel_order(order, request_id)
            flogger.info("order cancellation success",
                         request_id=request_id, order_id=order.order_id)
            return True
        except Exception as e:
            flogger.error("order cancellation failed", request_id=request_id,
                          order_id=order.order_id, error=str(e))
            return False

    # 回调函数，由Front API调用

    def modify_instrument_param_value(self, instrument_param_value: InstrumentParamValueEntity, request_id: int):
        """修改合约参数"""
        flogger.debug("modifying instrument param via front api",
                      request_id=request_id, instrument_param_value=instrument_param_value)
        try:
            self.gateway.do_modify_instrument_param_value(
                instrument_param_value, request_id)
            flogger.info("modify instrument param success", instrument_id=instrument_param_value.instrument_id,
                         param_key=instrument_param_value.param_key, param_value=instrument_param_value.param_value, request_id=request_id)
            return True
        except Exception as e:
            flogger.error("modify instrument param failed", instrument_id=instrument_param_value.instrument_id,
                          param_key=instrument_param_value.param_key, param_value=instrument_param_value.param_value, request_id=request_id, error=str(e))

    def modify_custom_param_value(self, custom_param_value: CustomParamValueEntity, request_id: int):
        """修改自定义参数"""
        flogger.debug("modifying custom param via front api",
                      request_id=request_id, custom_param_value=custom_param_value)
        try:
            self.gateway.do_modify_custom_param_value(
                custom_param_value, request_id)
            flogger.info("modify custom param success", custom_id=custom_param_value.custom_id,
                         param_key=custom_param_value.param_key, param_value=custom_param_value.param_value, request_id=request_id)
            return True
        except Exception as e:
            flogger.error("modify custom param failed", custom_id=custom_param_value.custom_id, param_key=custom_param_value.param_key,
                          param_value=custom_param_value.param_value, request_id=request_id, error=str(e))

    def handle_front_connected(self, data: bytearray) -> None:
        flogger.info("front api connection established")

    def handle_front_disconnected(self, data: bytearray) -> None:
        flogger.info("front api disconnect")

    def handle_rsp_info(self, data: RspInfoMsg, request_id: int) -> None:
        # flogger.debug("received response info", request_id=request_id, data=data)
        pass

    def handle_rtn_market_data(self, data: MarketDataMsg) -> None:
        # flogger.debug("received market data", instrument_id=data.instrument_id)
        self.event_engine.put(Event(EVENT_MARKET_DATA, data))

    def handle_rtn_order(self, data: OrderMsg) -> None:
        flogger.debug("received order data", data=str(data))
        self.event_engine.put(Event(EVENT_ORDER, data))

    def handle_rtn_trade(self, data: TradeMsg) -> None:
        flogger.debug("received trade data", data=str(data))
        self.event_engine.put(Event(EVENT_TRADE, data))

    def handle_rtn_user(self, data: UserMsg) -> None:
        # flogger.debug("received user data", data=str(data))
        self.event_engine.put(Event(EVENT_USER, data))

    def handle_rtn_security_instrument(self, data: SecurityInstrumentMsg) -> None:
        # flogger.debug("received security instrument data", instrument_id=data.instrument_id)
        self.event_engine.put(Event(EVENT_SECURITY_INSTRUMENT, data))

    def handle_rtn_future_instrument(self, data: FutureInstrumentMsg) -> None:
        # flogger.debug("received future instrument data", instrument_id=data.instrument_id)
        self.event_engine.put(Event(EVENT_FUTURE_INSTRUMENT, data))

    def handle_rtn_option_instrument(self, data: OptionInstrumentMsg) -> None:
        # flogger.debug("received option instrument data", instrument_id=data.instrument_id)
        self.event_engine.put(Event(EVENT_OPTION_INSTRUMENT, data))

    def handle_rtn_position(self, data: PositionMsg) -> None:
        # flogger.debug("received position data", data=str(data))
        self.event_engine.put(Event(EVENT_POSITION, data))

    def handle_rtn_instrument_param_define(self, data: InstrumentParamDefineMsg) -> None:
        # flogger.debug("received instrument param define", data=str(data))
        pass

    def handle_rtn_instrument_param_value(self, data: InstrumentParamValueMsg) -> None:
        # flogger.debug("received instrument param value", data=str(data))
        self.event_engine.put(Event(EVENT_INSTRUMENT_PARAM_VALUE, data))

    def handle_rtn_custom_param_define(self, data: CustomParamDefineMsg) -> None:
        print(data)
        flogger.debug("received custom param define", data=str(data))

    def handle_rtn_custom_param_value(self, data: CustomParamValueMsg) -> None:
        print(data)
        flogger.debug("received custom param value", data=str(data))
        self.event_engine.put(Event(EVENT_CUSTOM_PARAM_VALUE, data))

    def handle_rtn_trading_account(self, data: TradingAccountMsg) -> None:
        print(data)
        flogger.debug("received trading account", data=str(data))
        self.event_engine.put(Event(EVENT_TRADING_ACCOUNT, data))
