'''
CFFEX Confidential.

@Copyright 2018 CFFEX.  All rights reserved.

The source code for this program is not published or otherwise
divested of its trade secrets, irrespective of what has been
deposited with the China Copyright Office.

Author:  Febao
Date:    2025-02-18 15:16:20
Version: 0.0.7
'''
from enum import Enum


class table_enum(Enum):
    """表格枚举"""
    exchange = 0X006F  # 产品信息
    product = 0X0008  # 产品信息
    option_serial = 0X0009  # 期权系列信息及基础属性
    security_instrument = 0X000C  # 现货合约
    option_instrument = 0X000B  # 期权合约
    future_instrument = 0X000A  # 期货合约信息及基础属性
    user = 0X0001  # 用户、用户角色基础信息
    market_data = 0X0015  # 原始市场行情
    order = 0X004E  # order指令流水
    trade = 0X004F  # 成交流水
    quote = 0X0050  # 报价流水
    position = 0X0039  # 合约持仓信息维护
    strategy_instance = 0X005D  # 策略实例维护
    instrument_param_value = 0X0052  # 合约参数参数值
    custom_param_value = 0X0053  # 自定义参数参数值
    trading_account = 0X0054  # 交易账户


class IntStatusEnum(Enum):
    '''None'''
    INACTIVE = '0'  # 非活跃
    ACTIVE = '1'  # 活跃


class ExchangeIdEnum(Enum):
    '''None'''
    EXCHANGE_UNKNOWN = 'n'  # 未知
    EXCHANGE_CFFEX = '0'  # 中金所
    EXCHANGE_SHFE = '1'  # 上期所
    EXCHANGE_DCE = '2'  # 大商所
    EXCHANGE_ZCE = '3'  # 郑商所
    EXCHANGE_SSE = '4'  # 上交所
    EXCHANGE_SZSE = '5'  # 深交所
    EXCHANGE_INE = '6'  # 能源交易中心
    EXCHANGE_GFEX = '7'  # 广期所
    EXCHANGE_BSE = '8'  # 北交所
    EXCHANGE_ALL = 'z'  # 全部


class OptionTypeEnum(Enum):
    '''None'''
    OPTION_CALL = 'c'  # 认购期权
    OPTION_PUT = 'p'  # 认沽期权


class InstrumentTypeEnum(Enum):
    '''None'''
    INSTRUMENT_UNKNOWN = 'u'  # 未知
    INSTRUMENT_FUTURE = '0'  # 期货
    INSTRUMENT_OPTION = '1'  # 期权
    INSTRUMENT_SECURITY = '2'  # 证券
    INSTRUMENT_ARBITRAGE = '3'  # 套利
    INSTRUMENT_TYPE_ALL = '9'  # 全部


class QuoteSourceEnum(Enum):
    '''None'''
    QUOTE_SOURCE_UNKNOWN = 'u'  # 未知
    QUOTE_SOURCE_ALL = 'A'  # 全部
    QUOTE_SOURCE_MANUAL_QUOTE = '1'  # 手动报价
    QUOTE_SOURCE_STRATEGY_QUOTE = '2'  # 策略报价
    QUOTE_SOURCE_OUTSIDE_QUOTE = '3'  # 外部报价


class OrderSourceEnum(Enum):
    '''None'''
    ORDER_UNKNOWN = 'u'  # 未知
    ORDER_SOURCE_ALL = 'A'  # 全部
    ORDER_SOURCE_MANUAL_ORDER = '1'  # 手动报单
    ORDER_SOURCE_MANUAL_QUOTE_ORDER = '2'  # 手动报价衍生单
    ORDER_SOURCE_STRATEGY_ORDER = '3'  # 策略报单
    ORDER_SOURCE_STRATEGY_QUOTE_ORDER = '4'  # 策略报价衍生单
    ORDER_SOURCE_OUTSIDE_ORDER = '5'  # 外部报单
    ORDER_SOURCE_OUTSIDE_QUOTE_ORDER = '6'  # 外部报价衍生单


class CancelSourceEnum(Enum):
    '''None'''
    CANCEL_SOURCE_UNKNOWN = 'u'  # 未知
    CANCEL_SOURCE_MANUAL_CANCEL = '1'  # 手动撤单
    CANCEL_SOURCE_STRATEGY_CANCEL = '2'  # 策略撤单
    CANCEL_SOURCE_OUTSIDE_CANCEL = '3'  # 外部撤单


class OffsetFlagEnum(Enum):
    '''None'''
    OFFSET_FLAG_AUTO = '5'  # 自动
    OFFSET_FLAG_OPEN = '1'  # 开
    OFFSET_FLAG_CLOSE = '2'  # 平
    OFFSET_FLAG_CLOSE_TODAY = '3'  # 平今
    OFFSET_FLAG_CLOSE_YESTERDAY = '4'  # 平昨


class HedgeFlagEnum(Enum):
    '''None'''
    HEDGE_FLAG_SPECULATION = '1'  # 投机
    HEDGE_FLAG_ARBITRAGE = '2'  # 套利
    HEDGE_FLAG_HEDGE = '3'  # 套保
    HEDGE_FLAG_MARKET_MAKER = '4'  # 做市商


class QuoteStatusEnum(Enum):
    '''None'''
    QUOTE_STATUS_NONE = '0'  # 未知状态
    QUOTE_STATUS_WAITING = '1'  # 已报价，待确认
    QUOTE_STATUS_IN_BOOK = '2'  # 交易所确认
    QUOTE_STATUS_PART_CANCEL = '3'  # 部分报价单取消
    QUOTE_STATUS_ALL_CANCEL = '4'  # 全部报价单取消
    QUOTE_STATUS_ERROR = '5'  # 报价单错误
    QUOTE_STATUS_PART_TRADED = '6'  # 部分成交
    QUOTE_STATUS_ALL_TRADED = '7'  # 全部成交
    QUOTE_STATUS_TIMEOUT = '8'  # 超时状态


class OrderStatusEnum(Enum):
    '''None'''
    ORDER_STATUS_NONE = '0'  # 未知状态
    ORDER_STATUS_WAITING = '1'  # 已报单，待确认
    ORDER_STATUS_IN_BOOK = '2'  # 交易所确认
    ORDER_STATUS_CANCEL = '3'  # 订单取消
    ORDER_STATUS_ERROR = '4'  # 订单错误
    ORDER_STATUS_PART_TRADED = '5'  # 部分成交
    ORDER_STATUS_ALL_TRADED = '6'  # 全部成交
    ORDER_STATUS_TIMEOUT = '7'  # 超时状态


class DirectionEnum(Enum):
    '''None'''
    DIRECTION_BUY = '0'  # 买
    DIRECTION_SELL = '1'  # 卖


class PriceCategoryEnum(Enum):
    '''None'''
    PRICE_CATEGORY_LIMIT = '2'  # 限价
    PRICE_CATEGORY_ANY = '1'  # 任意价
    PRICE_CATEGORY_BEST = '3'  # 对手方最优价
    PRICE_CATEGORY_FIVE_LEVEL = '4'  # 最优五档
    PRICE_CATEGORY_ARBITRAGE = '5'  # 套利
    PRICE_CATEGORY_SWAP = '6'  # 互换
    PRICE_CATEGORY_BOTH = '7'  # 报价衍生
    PRICE_CATEGORY_OWN_BEST = '9'  # 本方最优价


class TimeConditionEnum(Enum):
    '''None'''
    TIME_CONDITION_GFD = '2'  # 当日有效
    TIME_CONDITION_IOC = '1'  # 立即完成否则撤销
    TIME_CONDITION_GIS = '3'  # 小节有效


class VolumeConditionEnum(Enum):
    '''None'''
    VOLUME_ANY = '1'  # 任何数量
    VOLUME_COMPLETE = '2'  # 全部数量


class HedgePriceEnum(Enum):
    '''None'''
    BEST_PRICE = '1'  # 最优价
    OPPONENT_PRICE = '2'  # 对手最优价
    LAST_PRICE = '3'  # 最新价
    LIMIT_PRICE = '4'  # 限价


class RoudingModeEnum(Enum):
    '''None'''
    ROUNDING_MODEL_ROUND = '3'  # 四舍五入
    ROUNDING_MODEL_CEIL = '1'  # 舍入， 向上取整
    ROUNDING_MODEL_FLOOR = '2'  # 舍出， 向下取整


class SpreadTemplateTypeEnum(Enum):
    '''None'''
    QUOTE_SPREAD_TEMPLATE = '1'  # 报价价差模板
    INQUIRY_SPREAD_TEMPLATE = '2'  # 回应询价价差模板


class SpreadTemplateAlgorithmEnum(Enum):
    '''None'''
    TEMPLATE_ALGO_CONSTANT = '1'  # 常量价差模板
    TEMPLATE_ALGO_MAX = '2'  # 最大值价差模板


class UserTypeEnum(Enum):
    '''None'''
    ADMIN_TYPE = '1'  # 管理员类型
    TRADER_TYPE = '2'  # 交易员类型
    RISK_TYPE = '3'  # 风控员类型
    QUERIER_TYPE = '4'  # 查询员类型
    ACCOUNT_MANAGER_TYPE = '5'  # 账户管理员类型
    OPERATOR_MAINTAINER_TYPE = '6'  # 运维员类型


class UserSessionStatusEnum(Enum):
    '''None'''
    LOGIN_STATUS = '1'  # 已登录
    UNLOGIN_STATUS = '2'  # 未登录
    MULTI_LOGIN_STATUS = '3'  # 重复登录 当前会话失效
    PASS_OVERDUE_STATUS = '4'  # 密码重置 当前会话失效
    TIMEOUT_STATUS = '5'  # 心跳超时 当前会话失效
    INACTIVE_ACCOUNT_STATUS = '6'  # 帐号未激活 当前会话失效
    ACCOUNT_ROLE_CHANGED = '7'  # 帐号权限变更 当前会话失效


class LoginFlagEnum(Enum):
    '''None'''
    NORMAL_LOGIN = '0'  # 正常登录
    FORCE_LOGIN = '1'  # 强制登录


class StrategyStateEnum(Enum):
    '''None'''
    STRATEGY_INIT_STAT = '1'  # 初始状态
    STRATEGY_RUNNING_STAT = '2'  # 运行中
    STRATEGY_PAUSE_STAT = '3'  # 已暂停
    STRATEGY_DELETE_STAT = '4'  # 已删除
    STRATEGY_TIMEOUT_STAT = '5'  # 已失效


class StrategyLogLevelEnum(Enum):
    '''None'''
    STRATEGY_DEBUG = '0'  # 调试日志
    STRATEGY_INFO = '1'  # 正常日志
    STRATEGY_WARNING = '2'  # 警告日志
    STRATEGY_ERROR = '3'  # 错误日志


class InstrumentTradingStatusEnum(Enum):
    '''None'''
    UNKNOWN = '0'  # 未知状态
    BEFORE_TRADING = '1'  # 开盘前
    NOTRADING = '2'  # 非交易
    CONTINOUS = '3'  # 连续交易
    AUCTION_ORDERING = '4'  # 集合竞价报单
    AUCTION_MATCH = '5'  # 集合竞价撮合
    CLOSED = '6'  # 收盘
    SUSPENDED = '7'  # 停牌
    CIRCUIT_BREAKER = '8'  # 熔断
    VOLATILITY_DISRUPTION = '9'  # 波动性中断
    INQUIRY = 'A'  # 询价中
    CLOSED_AUCTION_ORDERING = 'B'  # 收盘集合竞价
    AFTER_TRADING = 'C'  # 盘后交易


class IsTradingDayEnum(Enum):
    '''None'''
    TRADING_DAY = '1'  # 交易日
    NON_TRADING_DAY = '0'  # 非交易日


class PositionTypeEnum(Enum):
    '''None'''
    LONG_POSITION = '0'  # 多头持仓
    SHORT_POSITION = '1'  # 空头持仓


class RiskCategoryEnum(Enum):
    '''None'''
    RISK_POSITION_LIMIT = '0'  # 持仓限额
    RISK_MAX_ORDER_LIMIT = '1'  # 最大报单限额
    RISK_FLOW_CONTROL = '2'  # 流控
    RISK_SELF_TRADE_INSIDE = '3'  # 系统内防对敲
    RISK_SELF_TRADE_OUTSIDE = '4'  # 系统间防对敲
    RISK_TRADING_RIGHT = '5'  # 交易权限
    RISK_GREEKS = '6'  # 希腊字母
    RISK_DEVIATION = '7'  # 价格偏离度
    RISK_PRICE_LIMIT = '8'  # 涨跌停限制
    RISK_MAX_CANCEL_ORDER_LIMIT = '9'  # 最大撤单限额


class PortfolioTypeEnum(Enum):
    '''None'''
    NO_VIRTUAL = '0'  # 非虚拟组合
    VIRTUAL = '1'  # 虚拟组合


class RateSourceTypeEnum(Enum):
    '''None'''
    FIX_RATE = '1'  # 固定利率
    PREDICT_RATE = '0'  # 利率曲线


class CloseTypeEnum(Enum):
    '''None'''
    PRFER_CLOSE_TODAY = '1'  # 优先平今
    PRFER_CLOSE_YESTODAY = '2'  # 优先平昨


class OutsideSelfTradeTypeEnum(Enum):
    '''None'''
    OUTSIDE_SELF_TRADE_NO_CHECK = '0'  # 不检查
    OUTSIDE_SELF_TRADE_CHECK_WAITING = '1'  # 待检查
    OUTSIDE_SELF_TRADE_CHECK_PASS = '2'  # 检查通过
    OUTSIDE_SELF_TRADE_CHECK_FAILED = '3'  # 检查不通过


class TradeSourceEnum(Enum):
    '''None'''
    TRADE_SOURCE_UNKNOWN = 'u'  # 未知
    TRADE_SOURCE_INIT = '1'  # 初始化
    TRADE_SOURCE_MANUAL = '2'  # 手动单
    TRADE_SOURCE_STRATEGY = '3'  # 策略单
    TRADE_SOURCE_OUTSIDE = '4'  # 外部流水
    TRADE_SOURCE_OUTSIDE_COMB = '5'  # 外部流水组合
    TRADE_SOURCE_BOOK = '6'  # 簿记
    TRADE_SOURCE_CREATION_REDEMPTION = '7'  # 簿记申赎
    TRADE_SOURCE_OUTSIDE_CREATION_REDEMPTION = '8'  # 外部流水申赎
    TRADE_SOURCE_SETTLEMENT = 'A'  # 结算成交
    TRADE_SOURCE_MARGIN_TRADING = 'B'  # 簿记融券可卖


class InquiryQuoteStatusEnum(Enum):
    '''None'''
    INQUIRY_QUOTE_WAITING = '0'  # 等待回应
    INQUIRY_QUOTE_FINISH = '1'  # 已回应
    INQUIRY_QUOTE_TIMEOUT = '2'  # 回应超时


class CombActionEnum(Enum):
    '''None'''
    COMB_COMBINE = '0'  # 组合申报
    COMB_SPLIT = '1'  # 组合拆分


class CombStrategyEnum(Enum):
    '''None'''
    COMB_STRATEGY_UNKNOWN = 'n'  # 未知
    COMB_STRATEGY_CNSJC = '0'  # 认购牛市价差
    COMB_STRATEGY_CXSJC = '1'  # 认购熊市价差
    COMB_STRATEGY_PNSJC = '2'  # 认沽牛市价差
    COMB_STRATEGY_PXSJC = '3'  # 认沽熊市价差
    COMB_STRATEGY_KS = '4'  # 跨式空头
    COMB_STRATEGY_KKS = '5'  # 宽跨式空头
    COMB_STRATEGY_SP = '6'  # 期货跨期
    COMB_STRATEGY_SPC = '7'  # 期货跨品种
    COMB_STRATEGY_DS = '8'  # 期货对锁
    COMB_STRATEGY_DSO = '9'  # 期权对锁
    COMB_STRATEGY_STD = 'a'  # 期权跨式
    COMB_STRATEGY_STG = 'b'  # 期权宽跨式
    COMB_STRATEGY_BVS = 'c'  # 买入垂直价差
    COMB_STRATEGY_SVS = 'd'  # 卖出垂直价差
    COMB_STRATEGY_PRT_BUY = 'e'  # 买入期权期货组合
    COMB_STRATEGY_PRT_SELL = 'f'  # 卖出期权期货组合
    COMB_STRATEGY_CS = 'g'  # 日历价差


class CombSourceEnum(Enum):
    '''None'''
    COMB_SOURCE_UNKNOWN = 'u'  # 未知
    COMB_SOURCE_INIT = '0'  # 初始组合
    COMB_SOURCE_MANUAL_COMB = '1'  # 手动组合
    COMB_SOURCE_STRATEGY_COMB = '2'  # 策略组合
    COMB_SOURCE_OUTSIDE_COMB = '3'  # 外部组合
    COMB_SOURCE_ALL = 'A'  # 全部


class CombStatusEnum(Enum):
    '''None'''
    COMB_STATUS_NONE = '0'  # 未知状态
    COMB_STATUS_WAITING = '1'  # 已申报，待确认
    COMB_STATUS_IN_COMB = '2'  # 组合成功
    COMB_STATUS_PART_SPLIT = '3'  # 部分拆分
    COMB_STATUS_ALL_SPLIT = '4'  # 全部拆分
    COMB_STATUS_ERROR = '5'  # 组合错误
    COMB_STATUS_TIMEOUT = '6'  # 超时状态


class ExpireDateTypeEnum(Enum):
    '''None'''
    RECENT_MONTH = '1'  # 当月合约
    FAR_MONTH = '2'  # 远月合约


class LevelEnum(Enum):
    '''None'''
    LEVEL_PORTFOLIO = '0'  # 组合
    LEVEL_INSTRUMENT = '1'  # 合约
    LEVEL_SERIAL = '2'  # 系列
    LEVEL_PRODUCT = '3'  # 产品
    LEVEL_EXCHANGE = '4'  # 交易所


class ProductTypeEnum(Enum):
    '''None'''
    PRODUCT_FUTURE = '1'  # 期货
    PRODUCT_OPTION = '2'  # 期权
    PRODUCT_SECURITY = '3'  # 现货
    PRODUCT_ARBITRAGE = '4'  # 套利组合


class BoolEnum(Enum):
    '''None'''
    MM_FALSE = '0'  # false
    MM_TRUE = '1'  # true


class PortfolioSourceTypeEnum(Enum):
    '''None'''
    PORTFOLIO_TODAY_TRADE = '1'  # 今成交默认组合
    PORTFOLIO_YESTERDAY_POSITION = '2'  # 昨持仓默认组合


class CounterIdEnum(Enum):
    '''None'''
    COUNTER_UNKNOWN = 'u'  # 未知
    COUNTER_FEMAS = '0'  # 飞马
    COUNTER_CTP = '1'  # CTP
    COUNTER_XONE = '2'  # 飞创
    COUNTER_TAP = '3'  # 易盛
    COUNTER_O32 = '4'  # 恒生O32
    COUNTER_FEMASOP = '5'  # 飞马期权
    COUNTER_YD = '6'  # 易达
    COUNTER_APEX = '7'  # 顶点
    COUNTER_YLINK = '8'  # 雁联
    COUNTER_ROOTNET = '9'  # 根网
    COUNTER_QDP = 'a'  # QDP
    COUNTER_ROHON = 'b'  # 融航
    COUNTER_SHENGLI = 'c'  # 盛立
    COUNTER_ATP = 'e'  # 华锐
    COUNTER_MOCK = 'f'  # MOCK
    COUNTER_EXFUTURE = 'g'  # 交易所模拟器期货版本
    COUNTER_SOP_MINI = 'h'  # 顶点期权
    COUNTER_EXSECURITY = 'i'  # 交易所模拟器期权版本
    COUNTER_ALL = 'z'  # 全部


class ExerciseTypeEnum(Enum):
    '''None'''
    PHYSICAL_DELIVERY = '1'  # 实物交割
    CASH_DELIVERY = '2'  # 现金交割


class ExerciseStyleEnum(Enum):
    '''None'''
    EXERCISE_STYLE_AMERICAN = '0'  # 美式
    EXERCISE_STYLE_EUROPEAN = '1'  # 欧式


class CurrentPriceTypeEnum(Enum):
    '''None'''
    CURRENT_PRICE_TYPE_LAST_PRICE = '1'  # 最新价
    CURRENT_PRICE_TYPE_MID_PRICE = '2'  # 中间价
    CURRENT_PRICE_TYPE_CLOSE_PRICE = '3'  # 收盘价
    CURRENT_PRICE_TYPE_UNKNOWN = 'n'  # 未配置


class AveragePositionPriceTypeEnum(Enum):
    '''None'''
    AVERAGE_POSITION_PRICE_TYPE_LAST_PRICE = '1'  # 最新价
    AVERAGE_POSITION_PRICE_TYPE_PRE_SETTLEMENT_PRICE = '2'  # 昨结价
    AVERAGE_POSITION_PRICE_TYPE_CLOSE_PRICE = '3'  # 收盘价
    AVERAGE_POSITION_PRICE_TYPE_DERIVED_PRICE = '4'  # 理论价


class FitContractTypeEnum(Enum):
    '''None'''
    FIT_CONTRACT_TYPE_ALL = '0'  # 全部合约
    FIT_CONTRACT_TYPE_CALL = '1'  # 看涨合约
    FIT_CONTRACT_TYPE_PUT = '2'  # 看跌合约
    FIT_CONTRACT_TYPE_VIRTUAL = '3'  # 虚值合约


class FitPriceTypeEnum(Enum):
    '''None'''
    FIT_PRICE_TYPE_LAST = '0'  # 最新价
    FIT_PRICE_TYPE_BID = '1'  # 买一价
    FIT_PRICE_TYPE_ASK = '2'  # 卖一价
    FIT_PRICE_TYPE_MID = '3'  # 中间价


class FitRangeTypeEnum(Enum):
    '''None'''
    FIT_RANGE_TYPE_STRIKE = '0'  # 执行价范围拟合
    FIT_RANGE_TYPE_DELTA = '1'  # delta范围拟合
    FIT_RANGE_TYPE_ATM = '2'  # 百分比范围拟合
    FIT_RANGE_TYPE_LOGMONEYNESS = '3'  # LogMoneyness范围拟合
    FIT_RANGE_TYPE_STRIKE_ABSOLUTE = '4'  # 执行价绝对值范围拟合


class AtmForwardTypeEnum(Enum):
    '''None'''
    FIX_ATM_FORWARD = '0'  # 固定值
    FOLLOW_ATM_FORWARD = '1'  # 跟踪值


class AtmForwardPriceTypeEnum(Enum):
    '''None'''
    ATM_FORWARD_LAST_PRICE = '0'  # 最新价
    ATM_FORWARD_BID_PRICE = '1'  # 买一价
    ATM_FORWARD_ASK_PRICE = '2'  # 卖一价
    ATM_FORWARD_MID_PRICE = '3'  # 中间价
    ATM_FORWARD_PRE_SETTLEMENT_PRICE = '4'  # 昨结价
    ATM_FORWARD_OPEN_PRICE = '5'  # 开盘价
    ATM_FORWARD_BASE_PRICE = '6'  # 基准价
    ATM_FORWARD_FORWARD_PRICE = '7'  # 远期价
    ATM_FORWARD_THEORETICAL_PRICE = '8'  # 理论价


class GreeksTypeEnum(Enum):
    '''None'''
    GREEKS_UNKNOW = '0'  # 未知
    DELTA = '1'  # delta
    GAMMA = '2'  # gamma
    VEGA = '3'  # vega
    CASH_DELTA = '4'  # cash_delta
    CASH_GAMMA = '5'  # cash_gama


class DeviationPriceTypeEnum(Enum):
    '''None'''
    PRICE_UNKNOWN = '0'  # 未知
    PRICE_THREO = '1'  # 理论价
    PRICE_MID = '2'  # 中间价
    PRICE_LAST = '3'  # 最新价


class MismatchTypeEnum(Enum):
    '''None'''
    MISMATCH_UNKNOW = '0'  # 未知
    MISMATCH_COUNTER = '1'  # 柜台
    MISMATCH_FEBAO = '2'  # 飞豹


class CheckPhaseTypeEnum(Enum):
    '''None'''
    CHECK_PHASE_UNKNOW = '0'  # 未知
    CHECK_PHASE_BEGIN = '1'  # 校验开始
    CHECK_PHASE_END = '2'  # 校验结束


class VolatilityParamSourceEnum(Enum):
    '''None'''
    VOLATILITY_PARAM_SOURCE_MANUAL = '0'  # 手动
    VOLATILITY_PARAM_SOURCE_AUTO = '1'  # 自动拟合
    VOLATILITY_PARAM_SOURCE_STRATEGY = '2'  # 策略


class SubstituteFlagEnum(Enum):
    '''None'''
    CASH_UNKNOWN = 'n'  # 未配置
    SH_NO_CASH = '0'  # 沪市禁止现金替代
    SH_CASH_ALLOW = '1'  # 沪市可以进行现金替代
    SH_CASH_ONLY = '2'  # 沪市必须用现金替代
    SZ_CASH_ALLOW = '3'  # 深市退补现金替代
    SZ_CASH_ONLY = '4'  # 深市必须用现金替代
    ELSE_CASH_ALLOW = '5'  # 非深沪市退补现金替代
    ELSE_CASH_ONLY = '6'  # 非深沪市必须用现金替代
    HK_CASH_ALLOW = '7'  # 港市退补现金替代
    HK_CASH_ONLY = '8'  # 港市必须用现金替代


class TradingTypeEnum(Enum):
    '''None'''
    T_UNKNOWN = 'n'  # 未配置
    T_0 = '0'  # T+0
    T_1 = '1'  # T+1


class SecurityClassEnum(Enum):
    '''None'''
    SECURITY_UNKNOWN = 'n'  # 未配置
    SECURITY_FUND = '0'  # 基金
    SECURITY_STOCK = '1'  # 股票
    SECURITY_BOND = '2'  # 债券


class SecuritySubClassEnum(Enum):
    '''None'''
    SECURITY_SUB_UNKNOWN = 'n'  # 未配置
    FUND_ETF = '0'  # ETF基金
    FUND_RET = '1'  # RET基金
    STOCK_KSH = '2'  # 科创板股票
    STOCK_BASE = '3'  # 基础层股票
    STOCK_INNOVATION = '4'  # 创新层股票
    STOCK_SELECT = '5'  # 精选层股票


class SettlementTypeEnum(Enum):
    '''None'''
    SETTLEMENT_TYPE_EXERCISE = '1'  # 行权
    SETTLEMENT_TYPE_ABANDON = '2'  # 放弃行权
    SETTLEMENT_TYPE_DELIVERY = '3'  # 交割


class MidRiskStatusEnum(Enum):
    '''None'''
    MID_RISK_STATUS_NORMAL = '1'  # 正常状态
    MID_RISK_STATUS_WARNING = '2'  # 报警状态
    MID_RISK_STATUS_FORBID = '3'  # 风控状态


class CancelTypeEnum(Enum):
    '''None'''
    BILATERAL = '0'  # 双方
    BID_UNILATERAL = '1'  # 单买
    ASK_UNILATERAL = '2'  # 单卖


class MidRiskConfigTypeEnum(Enum):
    '''None'''
    INSTRUMENTS_IN_PRODUCT = '1'  # 产品下的每个合约
    INSTRUMENTS_IN_OPTION_SERIAL = '2'  # 系列下的每个合约
    SPEC_INSTRUMENT = '3'  # 合约
    OPTION_SERIAL_IN_PRODUCT = '4'  # 产品下每个系列
    SPEC_OPTION_SERIAL = '5'  # 系列
    SEPC_PRODUCT = '6'  # 产品
    PRODUCTSUM_IN_TRADING_ACCOUNT = '7'  # 交易账户下产品总量


class AuthorityCategoryEnum(Enum):
    '''None'''
    READ = '0'  # 策略参数只读属性
    READ_WRITE = '1'  # 策略参数读写属性


class LastOperateSourceEnum(Enum):
    '''None'''
    TERMINAL = '0'  # 客户端
    STRATEGY = '1'  # 策略


class CancelRequestOperateTypeEnum(Enum):
    '''None'''
    CANCEL_ORDER = '0'  # 撤报单
    CANCEL_QUOTE = '1'  # 撤报价


class SystemStatusEnum(Enum):
    '''None'''
    SYSTEM_STATUS_PREPARING = '1'  # 系统准备中
    SYSTEM_STATUS_ERROR = '2'  # 系统错误
    SYSTEM_STATUS_RUNNING = '3'  # 系统运行中


class NodeStatusEnum(Enum):
    '''None'''
    NODE_PREPARING = '3'  # 准备中
    NODE_SUCCESS = '1'  # 就绪
    NODE_FAILED = '2'  # 失败


class TradingAccountStatusEnum(Enum):
    '''None'''
    TRADING_ACCOUNT_STATUS_NORMAL = '0'  # 正常交易
    TRADING_ACCOUNT_STATUS_EMERGENCY = '1'  # 紧急制动


class EmergencyStatusEnum(Enum):
    '''None'''
    EMERGENCY_START = '1'  # 开始执行
    EMERGENCY_COMPLETE = '2'  # 执行完成


class SystemSwitchCategoryNameEnum(Enum):
    '''None'''
    SWITCH_UNKNOWN = '0'  # 未知
    EMERGENCY_BRAKING = '1'  # 系统紧急制动开关
    AUTO_VERIFY = '2'  # 自动组合校验开关


class StategyStreamStatusEnum(Enum):
    '''None'''
    STREAM_ACTIVE = '1'  # 订阅消息
    STREAM_UNACTIVE = '0'  # 取消订阅


class ExchangeUpdateSourceEnum(Enum):
    '''None'''
    UPDATE_BY_INIT = '1'  # 初始化
    UPDATE_BY_MD = '2'  # 行情
    UPDATE_BY_TRADE = '3'  # 交易
    UPDATE_BY_CLIENT = '4'  # 客户端


class ExecActionEnum(Enum):
    '''None'''
    OPTION_EXEC = '0'  # 期权执行
    OPTION_ABANDON = '1'  # 放弃执行


class AutocloseFlagEnum(Enum):
    '''None'''
    AUTOCLOSE_FLAG_OFF = '0'  # 不对冲
    AUTOCLOSE_FLAG_ON = '1'  # 对冲


class ExecStatusEnum(Enum):
    '''None'''
    EXEC_STATUS_NONE = '0'  # 未知状态
    EXEC_STATUS_WAITING = '1'  # 已申报，待确认
    EXEC_STATUS_IN_BOOK = '2'  # 交易所已确认
    EXEC_STATUS_CANCEL = '3'  # 已取消
    EXEC_STATUS_ERROR = '4'  # 行权申请错误
    EXEC_STATUS_TIMEOUT = '5'  # 超时状态


class StrategyPauseReasonEnum(Enum):
    '''None'''
    PAUSE_BY_UNKNOWN = '0'  # 未知
    PAUSE_BY_USER_OPERATOR = '1'  # 用户手动暂停
    PAUSE_BY_EMERGENCY = '2'  # 紧急制动暂停
    PAUSE_BY_STRATEGY_OPERATOR = '3'  # 策略主动暂停
    PAUSE_BY_TIMEOUT = '4'  # 中后台连接超时暂停


class SceneAnalysisResultEnum(Enum):
    '''None'''
    UNKOWN_TYPE = '0'  # 未知
    PNL_TYPE = '1'  # pnl
    DELTA_TYPE = '2'  # delta
    GAMMA_TYPE = '3'  # gamma
    VEGA_TYPE = '4'  # vega
    THETA_TYPE = '5'  # theta


class SentFlagEnum(Enum):
    '''None'''
    NOT_SENT = '0'  # None
    SENT = '1'  # None


class SelfTradeModeEnum(Enum):
    '''None'''
    STRICT_MODE = 's'  # None
    EXCELLENT_MODE = 'e'  # None


class LifeCycleEnum(Enum):
    '''None'''
    LIFE_CYCLE_UNKNOWN = '0'  # 未知
    LIFE_CYCLE_NEW = '1'  # 新录入
    LIFE_CYCLE_EXISTED = '2'  # 已存在
    LIFE_CYCLE_EXPIRING = '3'  # 到期


class TriggerTypeEnum(Enum):
    '''None'''
    NO_TIMER_TRIGGER = '0'  # 非定时器触发
    TIMER_TRIGGER = '1'  # 定时器触发


class ProxyQueryTypeEnum(Enum):
    '''None'''
    PROXY_QUERY_UNKNOWN = '0'  # 未知
    PROXY_QUERY_INIT = '1'  # 初始化查询
    PROXY_QUERY_TIMER = '2'  # 定时查询
    PROXY_QUERY_OUTER = '3'  # 外部查询


class ProxyQueryReqTypeEnum(Enum):
    '''None'''
    PROXY_QUERY_REQ_UNKNOWN = '0'  # 未知
    PROXY_QUERY_REQ_TRADE = '1'  # 查询成交
    PROXY_QUERY_REQ_POSITION = '2'  # 查询持仓
    PROXY_QUERY_REQ_SECURITY_POSITION = '3'  # 查询证券持仓
    PROXY_QUERY_REQ_COMB_DETAIL = '4'  # 查询组保明细
    PROXY_QUERY_REQ_COMB_STRATEGY_POSITION = '5'  # 查询组保策略持仓
    PROXY_QUERY_REQ_EXEC_ORDER = '6'  # 查询行权单
    PROXY_QUERY_REQ_OPTION_AUTOCLOSE = '7'  # 查询自动行权
    PROXY_QUERY_REQ_ACCOUNT_FUND = '8'  # 查询资金


class ConnectionStatusEnum(Enum):
    '''None'''
    LOGOUT = '0'  # 登出
    LOGIN = '1'  # 登录


class FitCurveTypeEnum(Enum):
    '''None'''
    FIT_CURVE_SINGLE = '0'  # 单曲线拟合
    FIT_CURVE_DOUBLE_CALL = '1'  # 双曲线拟合-CALL
    FIT_CURVE_DOUBLE_PUT = '2'  # 双曲线拟合-PUT


class VolatilityParamTypeEnum(Enum):
    '''None'''
    VOLATILITY_FIX_MODEL_PARAM = '0'  # 固定模型参数
    VOLATILITY_VARIABLE_MODEL_PARAM = '1'  # 可变模型参数


class VolatilityModifyModeEnum(Enum):
    '''None'''
    VOLATILITY_FIX_MODE = '0'  # 固定模式
    VOLATILITY_VARIABLE_MODE = '1'  # 可变模式


class BizDomainTypeEnum(Enum):
    '''None'''
    BIZ_DOMAIN_UNKOWN = 'n'  # 未知
    BIZ_DOMAIN_GLOBAL = '0'  # 全局业务域
    BIZ_DOMAIN_QUOTE = '1'  # 报价业务域
    BIZ_DOMAIN_RISK = '2'  # 风控业务域


class NotifyLogModeEnum(Enum):
    '''None'''
    NOTIFY_LOG_MODE_TEXT = '0'  # 文本通知
    NOTIFY_LOG_MODE_SOUND = '1'  # 声音通知


class InquiryStatusEnum(Enum):
    '''None'''
    INQUIRY_STATUS_NONE = '0'  # 未知状态
    INQUIRY_STATUS_WAITING = '1'  # 等待回应
    INQUIRY_STATUS_SUCCESS = '2'  # 询价请求成功
    INQUIRY_STATUS_ERROR = '3'  # 询价请求错误
    INQUIRY_STATUS_TIMEOUT = '4'  # 请求超时
