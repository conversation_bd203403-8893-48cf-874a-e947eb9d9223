'''
CFFEX Confidential.

@Copyright 2018 CFFEX.  All rights reserved.

The source code for this program is not published or otherwise
divested of its trade secrets, irrespective of what has been
deposited with the China Copyright Office.

Author:  Febao
Date:    2025-06-12 08:45:02
Version: 0.0.7
'''
from pydantic import BaseModel, ConfigDict
from .enum import *


class StrategyInstanceMsg(BaseModel):
    '''策略实例维护'''
    strategy_instance_id: int  # 策略实例id
    strategy_name: str  # 策略名
    strategy_engine_id: int  # 策略引擎id
    strategy_instance_name: str  # 实例名
    user_id: int  # 用户名关联id
    # 策略实例状态，1-初始状态，2-运行中，3-已暂停，4-已删除，5-已失效
    strategy_instance_state: StrategyStateEnum
    trading_account_id: int  # 交易账户id
    strategy_instance_priority: int  # 实例订阅消息接收优先级，分为：最高、高、中、低、最低五档
    last_operator_id: int  # 最近操作人id
    strategy_pause_reason: StrategyPauseReasonEnum  # 策略暂停理由


class StrategyDeployMsg(BaseModel):
    strategy_name: str  # 策略名
    strategy_engine_id: int  # 策略引擎id
    strategy_version: str  # 策略版本
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效
    last_operator_id: int  # 最近操作人id


class StrategyParamMsg(BaseModel):
    strategy_name: str  # 策略名
    param_key: str  # 参数名
    param_type: str  # 参数类型
    param_value: str  # 参数值
    index: int  # 参数序号
    tick: float  # 最小变动单位
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class StrategyEnumMsg(BaseModel):
    strategy_name: str  # 策略名
    enum_name: str  # 枚举名
    enum_value: str  # 枚举名
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class StrategyInstanceParamMsg(BaseModel):
    strategy_instance_id: int  # 策略实例id
    strategy_engine_id: int  # 策略引擎id
    param_key: str  # 参数名
    param_value: str  # 参数值
    last_operator_id: int  # 最近操作人id


class RspInfoMsg(BaseModel):
    '''通用响应消息'''
    error_id: int  # None
    error_msg: str  # None


class MarketDataMsg(BaseModel):
    '''原始市场行情'''
    instrument_id: str  # 合约id
    # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    exchange_id: ExchangeIdEnum
    update_sec: int  # 更新秒
    update_msec: int  # 更新毫秒
    pre_settlement: float | None  # 昨结算价
    pre_close: float | None  # 昨收盘价
    open: float | None  # 开盘价
    close: float | None  # 收盘价
    upper_limit_price: float | None  # 涨停价
    down_limit_price: float | None  # 跌停价
    high_price: float | None  # 最高价
    low_price: float | None  # 最低价
    last_price: float | None  # 最新价
    volume: int  # 累计成交量
    turn_over: float | None  # 累计成交金额
    pre_open_interest: float | None  # 昨持仓量
    open_interest: float | None  # 持仓量
    bid1_price: float | None  # 买一价
    ask1_price: float | None  # 卖一价
    bid1_volume: int  # 买一量
    ask1_volume: int  # 卖一量
    bid2_price: float | None  # 买二价
    ask2_price: float | None  # 卖二价
    bid2_volume: int  # 买二量
    ask2_volume: int  # 卖二量
    bid3_price: float | None  # 买三价
    ask3_price: float | None  # 卖三价
    bid3_volume: int  # 买三量
    ask3_volume: int  # 卖三量
    bid4_price: float | None  # 买四价
    ask4_price: float | None  # 卖四价
    bid4_volume: int  # 买四量
    ask4_volume: int  # 卖四量
    bid5_price: float | None  # 买五价
    ask5_price: float | None  # 卖五价
    bid5_volume: int  # 买五量
    ask5_volume: int  # 卖五量
    bid6_price: float | None  # 买六价
    ask6_price: float | None  # 卖六价
    bid6_volume: int  # 买六量
    ask6_volume: int  # 卖六量
    bid7_price: float | None  # 买七价
    ask7_price: float | None  # 卖七价
    bid7_volume: int  # 买七量
    ask7_volume: int  # 卖七量
    bid8_price: float | None  # 买八价
    ask8_price: float | None  # 卖八价
    bid8_volume: int  # 买八量
    ask8_volume: int  # 卖八量
    bid9_price: float | None  # 买九价
    ask9_price: float | None  # 卖九价
    bid9_volume: int  # 买九量
    ask9_volume: int  # 卖九量
    bid10_price: float | None  # 买十价
    ask10_price: float | None  # 卖十价
    bid10_volume: int  # 买十量
    ask10_volume: int  # 卖十量
    iopv: float | None  # 基金价格算法得出的价格（证券行情）
    dynamic_reference_price: float | None  # 动态参考价（证券行情）
    local_timestamp: int  # timestamp


class PositionMsg(BaseModel):
    '''合约持仓信息维护'''
    instrument_id: str  # 合约id
    trading_account_id: int  # 交易账户id
    long_position: int  # 多仓
    short_position: int  # 空仓
    frozen_long_position: int  # 冻结多仓
    frozen_short_position: int  # 冻结空仓
    td_long_position: int  # 今多仓
    td_short_position: int  # 今空仓
    frozen_td_long_position: int  # 冻结今多仓
    frozen_td_short_position: int  # 冻结今空仓
    yd_long_position: int  # 昨多仓
    yd_short_position: int  # 昨空仓
    frozen_yd_long_position: int  # 冻结昨多仓
    frozen_yd_short_position: int  # 冻结昨空仓
    long_comb_position: int  # 组合多仓
    short_comb_position: int  # 组合空仓
    buy_open_position: int  # 买开仓
    option_exec_position: int  # 期权行权仓
    option_abandon_position: int  # 期权放弃行权仓
    td_option_exec_position: int  # 今期权行权仓
    td_option_abandon_position: int  # 今期权放弃行权仓
    yd_option_exec_position: int  # 昨期权行权仓
    yd_option_abandon_position: int  # 昨期权放弃行权仓
    sell_open_position: int  # 卖开仓
    frozen_sell_open_position: int  # 在途卖开仓量
    frozen_buy_open_position: int  # 在途买开仓量


class OrderMsg(BaseModel):
    '''order指令流水'''
    instrument_id: str  # 合约id
    direction: DirectionEnum  # 买卖方向，0-买，1-卖
    offset_flag: OffsetFlagEnum  # 开平标志，1-开，2-平，3-平今，4-平昨，5-自动开平
    hedge_flag: HedgeFlagEnum  # 投机套保标志，1-投机，2-套利，3-套保，4-做市商
    price: float | None  # 成交价
    volume: int  # 累计成交量
    # 价格类型，1-市价，2-限价，3-对手方最优价，4-最优五档，5-套利，6-互换，7-报价衍生，8-其他，9-本方最优价
    price_category: PriceCategoryEnum
    time_condition: TimeConditionEnum  # 时间类型，1-IOC，2-GFD, 3-GIS
    volume_condition: VolumeConditionEnum  # 成交数量类型，1-部分成交，2-全部成交
    portfolio_id: int  # 组合id
    order_id: int  # 飞豹报单编号
    priority: int  # 优先级
    custom_flag: str  # 自定义标识
    seat_no: str  # 席位号
    trading_account_id: int  # 交易账户id
    investor_id: str  # 资金账户id
    order_sys_id: str  # 交易所报单编号
    # 报单状态，0-未知状态，1-已报单，待确认，2-交易所确认，3-订单取消，4-订单错误，5-部分成交，6-全部成交，7-超时状态
    order_status: OrderStatusEnum
    user_id: int  # 用户名关联id
    action_user_id: int  # 报单操作用户id
    insert_time: int  # 交易所插入时间
    update_time: int  # 交易所更新时间
    local_create_time: int  # 服务器创建时间
    local_insert_time: int  # 服务器插入时间
    local_update_time: int  # 服务器更新时间
    # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    exchange_id: ExchangeIdEnum
    traded_volume: int  # 成交量
    cancel_volume: int  # 撤单量
    # 报单来源，1-手动报单，2-手动报价单，3-策略报单，4-策略报价单，5-外部报单，6-外部报价单，A-全部来源
    order_source: OrderSourceEnum
    strategy_instance_id: int  # 策略实例id
    last_operator_id: int  # 最近操作人id
    error_id: int  # 错误id


class UserMsg(BaseModel):
    '''用户、用户角色基础信息，'''
    user_id: int  # 用户名关联id
    user_name: str  # 登录用户名
    user_role: UserTypeEnum  # 用户绑定角色信息，1-管理员类型，2-交易员类型，3-风控员类型，4-查询员类型，5-账户管理员类型，
    default_trading_account_id: int  # 默认交易账户，用于客户端打开默认展示
    active_flag: IntStatusEnum  # 账户正常/冻结标识
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效
    last_operator_id: int  # 最近操作人id
    create_time: str  # 账户创建时间


class UserPasswordMsg(BaseModel):
    '''用户对应密码信息，用于登录用户认证'''
    user_id: int  # 用户名关联id
    password: str  # 用户密码
    last_operator_id: int  # 最近操作人id


class TradingAccountMsg(BaseModel):
    '''交易账户id和名称维护'''
    trading_account_id: int  # 交易账户id
    trading_account_name: str  # 交易账户名
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class TraderAccountConfigMsg(BaseModel):
    '''用户绑定交易账户关系，用于数据读取权限'''
    user_id: int  # 用户名关联id
    trading_account_id: int  # 交易账户id
    last_operator_id: int  # 最近操作人id
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class SecurityInstrumentMsg(BaseModel):
    '''现货合约信息及基础属性'''
    exchange_id: ExchangeIdEnum  # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    product_id: str  # 合约对应品种id
    instrument_id: str  # 合约id
    instrument_name: str  # 合约名
    security_class: SecurityClassEnum  # 现货种类，0-基金，1-股票，2-债券，n-未配置
    # 现货子种类，0-ETF基金，1-RET基金，2-科创板股票，3-新三板股票， 4-精选层股票， n-未配置
    security_sub_class: SecuritySubClassEnum
    market_maker_count: int  # 做市商数量
    multiple: float | None  # 合约乘数
    tick: float | None  # 最小变动价位
    unit: int  # 最小报价单位
    trading_type: TradingTypeEnum  # 交易类型，0-T+0,1-T+1,n-未配置
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效
    last_operator_id: int  # 最近操作人id


class FutureInstrumentMsg(BaseModel):
    '''期货合约信息及基础属性'''
    exchange_id: ExchangeIdEnum  # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    product_id: str  # 品种id
    instrument_id: str  # 合约id
    instrument_name: str  # 合约名
    multiple: float | None  # 合约乘数
    tick: float | None  # 最小变动价位
    expire_date: str  # 到期日
    expire_date_type: ExpireDateTypeEnum  # 到期日类型，1-当月合约，2-远月合约
    exercise_type: ExerciseTypeEnum  # 交割方式，1-实物交割，2-现金交割 默认2
    is_dominant: IntStatusEnum  # 期货主力合约标注
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效
    last_operator_id: int  # 最近操作人id


class OptionInstrumentMsg(BaseModel):
    '''期权合约信息及基础属性'''
    exchange_id: ExchangeIdEnum  # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    product_id: str  # 品种id
    option_serial_id: str  # 期权系列id
    instrument_id: str  # 合约id
    instrument_name: str  # 合约名
    multiple: float | None  # 合约乘数
    tick: float | None  # 最小变动价位
    strike_price: float | None  # 行权价
    expire_date: str  # 到期日
    option_type: OptionTypeEnum  # 期权合约类型，c-call，p-put
    expire_date_type: ExpireDateTypeEnum  # 到期日类型，1-当月合约，2-远月合约
    underlying_instrument_id: str  # 标的合约id
    exercise_type: ExerciseTypeEnum  # 交割方式，1-实物交割，2-现金交割 默认2
    exercise_style: ExerciseStyleEnum  # 行权方式，0-美式，1-欧式 默认0
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效
    last_operator_id: int  # 最近操作人id


class InstrumentParamValueMsg(BaseModel):
    '''合约参数参数值'''
    instrument_id: str  # 合约id
    trading_account_id: int  # 交易账户id
    param_key: str  # 参数名
    param_value: str  # 参数值
    last_operator_id: int  # 最近操作人id
    last_operate_source: LastOperateSourceEnum  # 最近操作来源，0-客户端，1-策略
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class InstrumentParamDefineMsg(BaseModel):
    '''合约参数定义'''
    trading_account_id: int  # 交易账户id
    param_key: str  # 参数名
    param_type: str  # 参数类型
    authority_category: AuthorityCategoryEnum  # 权限类别，0-策略参数只读属性，1-策略参数读写属性
    enum_define: str  # 枚举定义
    last_operator_id: int  # 最近操作人id
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class CustomParamDefineMsg(BaseModel):
    '''自定义参数参数定义'''
    trading_account_id: int  # 交易账户id
    param_key: str  # 参数名
    param_type: str  # 参数类型
    authority_category: AuthorityCategoryEnum  # 权限类别，0-策略参数只读属性，1-策略参数读写属性
    enum_define: str  # 枚举定义
    last_operator_id: int  # 最近操作人id
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class CustomParamValueMsg(BaseModel):
    '''自定义参数参数值'''
    custom_id: str  # 自定义参数id
    trading_account_id: int  # 交易账户id
    param_key: str  # 参数名
    param_value: str  # 参数值
    last_operator_id: int  # 最近操作人id
    last_operate_source: LastOperateSourceEnum  # 最近操作来源，0-客户端，1-策略
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class ProductMsg(BaseModel):
    '''产品信息'''
    exchange_id: ExchangeIdEnum  # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    product_id: str  # 品种id
    product_name: str  # 产品名
    product_type: ProductTypeEnum  # 产品类型，1-期货，2-期权，3-现货，4-套利组合


class NotifyCancelOrderMsg(BaseModel):
    '''order撤单错误通知'''
    order_id: int  # 飞豹报单编号
    instrument_id: str  # 合约id
    trading_account_id: int  # 交易账户id
    error_id: int  # 错误id


class TradeMsg(BaseModel):
    '''成交流水'''
    exchange_id: ExchangeIdEnum  # 交易所id，0-中金所，1-上期所，2-大商所，3-郑商所，4-上交所，5-深交所，6-能源交易中心，7-广期所，n-未知，z-全部
    trade_id: int  # 成交id
    order_id: int  # 飞豹报单编号
    order_sys_id: str  # 交易所报单编号
    trade_sys_id: str  # 交易所成交编号
    instrument_id: str  # 合约id
    direction: DirectionEnum  # 买卖方向，0-买，1-卖
    price: float | None  # 成交价
    cost_price: float | None  # 成交成本
    volume: int  # 累计成交量
    user_id: int  # 用户名关联id
    portfolio_id: int  # 组合id
    profit: float | None  # 盈亏
    strategy_instance_id: int  # 策略实例id
    # 成交来源，1-初始化，2-手动单，3-策略单，4-外部流水，5-外部流水组合，6-簿记，7-簿记申赎，8-外部流水申赎，A-结算成交，B-簿记融券可卖
    trade_source: TradeSourceEnum
    trade_status: IntStatusEnum  # 成交状态
    close_td_volume: int  # 平今仓量
    close_yd_volume: int  # 平昨仓量
    trade_time: int  # 交易所成交时间
    local_insert_time: int  # 服务器插入时间
    local_update_time: int  # 服务器更新时间
    custom_flag: str  # 自定义标识
    # 价格类型，1-任意价，2-限价，3-对手方最优价，4-对手方最优五档，5-套利，6-互换，7-报价衍生，8-其他, 9-本方最优价
    price_category: PriceCategoryEnum
    trade_without_order: BoolEnum  # 成交是否有报单信息 0-有 1-无
    trading_account_id: int  # 交易账户id
    investor_id: str  # 资金账户id
    last_operator_id: int  # 最近操作人id


class InvestorAccountFundMsg(BaseModel):
    '''资金信息'''
    investor_account_id: str  # 投资者资金账户id
    broker_id: str  # 代理人id
    # 柜台id，0-FEMAS，1-CTP，2-XONE，3-TAP，4-O32，5-FEMASOP，6-YD，7-APEX，8-YLINK，9-ROOTNET，a-QDP，b-ROHON，u-未知，z-全部
    counter_id: CounterIdEnum
    available_fund: float | None  # 可用资金
    trading_account_id: int  # 交易账户id
    local_update_time: int  # 服务器更新时间


class AccountProductConfigMsg(BaseModel):
    '''交易账户对应品种关系'''
    trading_account_id: int  # 交易账户id
    product_id: str  # 品种id
    status: IntStatusEnum  # 记录有效状态，0-无效，1-有效


class FbOrderEntity(BaseModel):
    '''报单结构体'''
    model_config = ConfigDict(use_enum_values=True)
    instrument_id: str  # 合约id
    direction: DirectionEnum  # 买卖方向，0-买，1-卖
    offset_flag: OffsetFlagEnum  # 开平标志，1-开，2-平，3-平今，4-平昨，5-自动开平
    hedge_flag: HedgeFlagEnum  # 投机套保标志，1-投机，2-套利，3-套保，4-做市商
    price: float | None  # 成交价
    volume: int  # 累计成交量
    # 价格类型，1-市价，2-限价，3-对手方最优价，4-最优五档，5-套利，6-互换，7-报价衍生，8-其他，9-本方最优价
    price_category: PriceCategoryEnum
    time_condition: TimeConditionEnum  # 时间类型，1-IOC，2-GFD, 3-GIS
    volume_condition: VolumeConditionEnum  # 成交数量类型，1-部分成交，2-全部成交
    portfolio_id: int  # 组合id
    custom_flag: str  # 自定义标识
    seat_no: str  # 席位号
    investor_id: str  # 资金账户id


class FbCancelOrderEntity(BaseModel):
    '''撤单结构体'''
    model_config = ConfigDict(use_enum_values=True)
    instrument_id: str  # 合约id
    order_id: int  # 飞豹报单编号


class FbInstrumentParamValueEntity(BaseModel):
    '''合约参数参数值'''
    model_config = ConfigDict(use_enum_values=True)
    instrument_id: str  # 合约id
    param_key: str  # 参数名
    param_value: str  # 参数值


class FbCustomParamValueEntity(BaseModel):
    '''自定义参数参数值'''
    model_config = ConfigDict(use_enum_values=True)
    custom_id: str  # 自定义参数id
    param_key: str  # 参数名
    param_value: str  # 参数值
