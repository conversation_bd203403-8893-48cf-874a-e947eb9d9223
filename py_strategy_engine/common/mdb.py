from typing import Dict, List, Optional, Any, Callable, Type, TypeVar, Generic
from common.table_enum import table_enum
from utils.flog import flogger

T = TypeVar('T')


class Table(Generic[T]):
    """表格类，用于存储和管理记录"""

    def __init__(self, pk_fn: Callable[[T], str], model_type: Type[T] = None):
        self.records: Dict[str, T] = {}
        self.add_callbacks: List[Callable[[T], None]] = []
        self.update_callbacks: List[Callable[[T, T], None]] = []
        self.delete_callbacks: List[Callable[[T], None]] = []
        self._pk_fn = pk_fn
        self._model_type = model_type
        self._query_fields = self._extract_query_fields_from_pk_function(pk_fn)

    def register_add_commit_trigger(self, callback: Callable[[T], None]):
        """注册添加记录回调"""
        self.add_callbacks.append(callback)

    def register_update_commit_trigger(self, callback: Callable[[T, T], None]):
        """注册更新记录回调"""
        self.update_callbacks.append(callback)

    def register_delete_commit_trigger(self, callback: Callable[[T], None]):
        """注册删除记录回调"""
        self.delete_callbacks.append(callback)

    def add_record(self, record: T):
        """添加记录"""
        key = self._pk_fn(record)
        self.records[key] = record
        for callback in self.add_callbacks:
            callback(record)

    def update_record(self, record: T):
        """更新记录"""
        key = self._pk_fn(record)
        old_record = self.records.get(key)
        if old_record:
            self.records[key] = record
            for callback in self.update_callbacks:
                callback(old_record, record)
        else:
            self.add_record(record)

    def delete_record(self, record: T):
        """删除记录"""
        key = self._pk_fn(record)
        if key in self.records:
            old_record = self.records[key]
            del self.records[key]
            for callback in self.delete_callbacks:
                callback(old_record)

    def get(self, key: str) -> Optional[T]:
        """获取记录"""
        return self.records.get(key)

    def get_all(self) -> List[T]:
        """获取所有记录"""
        return list(self.records.values())

    def clear(self):
        """清空表格"""
        self.records.clear()

    def count(self) -> int:
        """获取记录数量"""
        return len(self.records)

    def filter(self, filter_fn: Callable[[T], bool]) -> List[T]:
        """过滤记录"""
        return [record for record in self.records.values() if filter_fn(record)]

    def _extract_query_fields_from_pk_function(self, pk_fn: Callable[[T], str]) -> List[str]:
        """从主键函数中提取出查询字段"""
        if hasattr(pk_fn, 'query_fields'):
            fields = pk_fn.query_fields
            flogger.debug("extract query fields from pk function",
                          function_name=pk_fn.__name__, query_fields=fields)
            return fields
        flogger.warning(
            "pk function has no query_fields attribute", function=pk_fn.__name__)
        return []


class MemoryDatabase:
    """内存数据库，管理所有表格"""

    def __init__(self):

        self.tables: Dict[table_enum, Table] = {}

    def register_table(self, table_id: table_enum, pk_fn: Callable[[Any], str], model_type: Type = None):
        """注册表格"""
        self.tables[table_id] = Table(pk_fn, model_type)
        flogger.info("registering table", table=table_id.name,
                     query_fields=self.tables[table_id]._query_fields)

    def get_table(self, table_id: table_enum) -> Optional[Table]:
        """获取表格"""
        return self.tables.get(table_id)

    def add_record(self, table_id: table_enum, record: Any):
        """添加记录"""
        table = self.get_table(table_id)
        if table:
            table.add_record(record)

    def update_record(self, table_id: table_enum, record: Any):
        """更新记录"""
        table = self.get_table(table_id)
        if table:
            table.update_record(record)

    def delete_record(self, table_id: table_enum, record: Any):
        """删除记录"""
        table = self.get_table(table_id)
        if table:
            table.delete_record(record)

    def get_record(self, table_id: table_enum, key: str) -> Optional[Any]:
        """获取记录"""
        table = self.get_table(table_id)
        if table:
            return table.get(key)
        return None

    def get_all_records(self, table_id: table_enum) -> List[Any]:
        """获取所有记录"""
        flogger.debug("get all records", table=table_id.name)
        table = self.get_table(table_id)
        if table:
            return table.get_all()
        return None

    def get_records_by_params(self, table_id: table_enum, **params) -> Optional[Any]:
        flogger.debug("get record by params",
                      table=table_id.name, params=params)
        table = self.get_table(table_id)
        if not table:
            flogger.warning("table not found", table_id=table_id)
            return None
        if params:
            param_keys = set(params.keys())
            query_fields_set = set(table._query_fields)
            if not param_keys.issubset(query_fields_set):
                invalid_params = param_keys - query_fields_set
                flogger.warning("invalid params", table=table_id.name,
                                invalid_params=invalid_params)
                return None
        try:
            temp_obj = self._create_temp_object(**params)
            if temp_obj:
                key = table._pk_fn(temp_obj)
                return table.get(key)
        except Exception as e:
            flogger.error("get record by params failed",
                          table_id=table_id, params=params, error=str(e))
            return None

    def _create_temp_object(self, **params):
        class TempObject:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
        return TempObject(**params)
