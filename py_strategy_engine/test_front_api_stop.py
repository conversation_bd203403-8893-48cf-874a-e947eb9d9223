#!/usr/bin/env python3
"""
测试front api停止对event engine的影响
"""

import time
import threading
from common.event import EventEngine, Event
from common.front_api import FrontApi
from utils.flog import flogger


class MockGateway:
    """模拟front gateway"""
    
    def __init__(self, front_api):
        self.front_api = front_api
        self.running = False
        self.thread = None
        
    def connect(self, pub_address, req_address, log_level, log_dir):
        """模拟连接"""
        print(f"[MockGateway] 连接到 {pub_address}, {req_address}")
        self.running = True
        # 启动模拟数据发送线程
        self.thread = threading.Thread(target=self._send_mock_data, daemon=True)
        self.thread.start()
        
    def _send_mock_data(self):
        """发送模拟市场数据"""
        count = 0
        while self.running:
            try:
                # 模拟市场数据
                mock_data = type('MockMarketData', (), {
                    'instrument_id': 'IF2501',
                    'last_price': 3000 + count % 100,
                    'timestamp': time.time()
                })()
                
                # 通过front api发送事件
                if hasattr(self.front_api, 'event_engine'):
                    event = Event(type="EVENT_MARKET_DATA", data=mock_data)
                    self.front_api.event_engine.put(event)
                
                count += 1
                time.sleep(0.01)  # 100Hz频率
                
                if count % 100 == 0:
                    print(f"[MockGateway] 已发送 {count} 条模拟数据")
                    
            except Exception as e:
                print(f"[MockGateway] 发送数据异常: {e}")
                break
    
    def close(self):
        """停止gateway"""
        print("[MockGateway] 调用 close()")
        self.running = False
        
    def join(self):
        """等待线程结束"""
        print("[MockGateway] 调用 join()")
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)
            if self.thread.is_alive():
                print("[MockGateway] 线程未能在2秒内结束")
            else:
                print("[MockGateway] 线程已结束")
        else:
            print("[MockGateway] 线程已经结束或不存在")


def test_front_api_stop_effect():
    """测试front api停止对event engine的影响"""
    print("=== 测试front api停止对event engine的影响 ===")
    
    # 创建event engine
    event_engine = EventEngine()
    
    # 创建front api
    front_api = FrontApi(event_engine)
    
    # 替换gateway为模拟版本
    front_api.gateway = MockGateway(front_api)
    
    # 注册事件处理器
    processed_count = 0
    def handle_market_data(event):
        nonlocal processed_count
        processed_count += 1
        if processed_count % 50 == 0:
            print(f"[Handler] 已处理 {processed_count} 条市场数据")
    
    event_engine.register("EVENT_MARKET_DATA", handle_market_data)
    
    # 启动event engine
    event_engine.start()
    
    # 模拟连接front api
    front_api.connect("tcp://localhost:5555", "tcp://localhost:5556", "info", "./log")
    
    print("让系统运行2秒，观察数据流...")
    time.sleep(2)
    
    print(f"运行2秒后，队列大小: {event_engine._queue.qsize()}")
    print(f"已处理事件数: {processed_count}")
    
    # 停止front api
    print("\n开始停止front api...")
    start_time = time.time()
    front_api.stop()
    stop_time = time.time() - start_time
    
    print(f"front api停止耗时: {stop_time:.2f} 秒")
    
    # 等待一小段时间，看看是否还有新事件
    time.sleep(1)
    queue_size_after_stop = event_engine._queue.qsize()
    processed_after_stop = processed_count
    
    print(f"front api停止后，队列大小: {queue_size_after_stop}")
    
    # 再等待1秒，确认没有新事件
    time.sleep(1)
    final_processed = processed_count
    
    print(f"最终处理事件数: {final_processed}")
    print(f"front api停止后是否还有新事件: {'是' if final_processed > processed_after_stop else '否'}")
    
    # 停止event engine
    print("\n开始停止event engine...")
    start_time = time.time()
    event_engine.stop()
    stop_time = time.time() - start_time
    
    print(f"event engine停止耗时: {stop_time:.2f} 秒")
    print(f"最终队列大小: {event_engine._queue.qsize()}")
    
    return stop_time < 3  # 应该在3秒内停止


def test_without_front_api_stop():
    """测试不停止front api的情况（对比测试）"""
    print("\n=== 对比测试：不停止front api的情况 ===")
    
    # 创建event engine
    event_engine = EventEngine()
    
    # 创建front api但不停止它
    front_api = FrontApi(event_engine)
    front_api.gateway = MockGateway(front_api)
    
    # 注册事件处理器
    processed_count = 0
    def handle_market_data(event):
        nonlocal processed_count
        processed_count += 1
        if processed_count % 50 == 0:
            print(f"[Handler] 已处理 {processed_count} 条市场数据")
    
    event_engine.register("EVENT_MARKET_DATA", handle_market_data)
    
    # 启动event engine
    event_engine.start()
    
    # 模拟连接front api
    front_api.connect("tcp://localhost:5555", "tcp://localhost:5556", "info", "./log")
    
    print("让系统运行2秒...")
    time.sleep(2)
    
    print(f"运行2秒后，队列大小: {event_engine._queue.qsize()}")
    print(f"已处理事件数: {processed_count}")
    
    # 直接停止event engine，不停止front api
    print("\n直接停止event engine（不停止front api）...")
    start_time = time.time()
    event_engine.stop()
    stop_time = time.time() - start_time
    
    print(f"event engine停止耗时: {stop_time:.2f} 秒")
    print(f"最终队列大小: {event_engine._queue.qsize()}")
    
    # 手动停止front api清理资源
    try:
        front_api.stop()
    except:
        pass
    
    return stop_time


def main():
    """运行测试"""
    print("开始测试front api停止对event engine的影响...")
    
    # 测试1：先停止front api再停止event engine
    result1 = test_front_api_stop_effect()
    
    time.sleep(2)  # 间隔
    
    # 测试2：不停止front api直接停止event engine
    result2 = test_without_front_api_stop()
    
    print(f"\n{'='*60}")
    print("测试结果对比:")
    print(f"先停止front api: {result1:.2f} 秒 {'✅ 快速' if result1 < 3 else '❌ 慢'}")
    print(f"不停止front api: {result2:.2f} 秒 {'✅ 快速' if result2 < 3 else '❌ 慢'}")
    
    if result1 < result2:
        print("🎉 先停止front api确实能加快event engine的停止速度！")
    else:
        print("🤔 停止front api的效果不明显，可能需要进一步优化")


if __name__ == "__main__":
    main()
