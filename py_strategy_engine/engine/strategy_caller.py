from common.front_api import Front<PERSON>pi
from common.strategy_instance_manager import StrategyInstanceManager
from py_strategy_api.fb_models import *
from pyfbapi_gateway.models import *
from pyfbapi_gateway.enum import *
from utils.id_generator import IdGenerator
from utils.model_converter import convert_model
from utils.flog import flogger


class StrategyCaller:
    """策略调用器，负责处理所有与策略调用相关的功能，如发送订单、撤销订单、修改参数等"""

    def __init__(self, front_api: FrontApi, strategy_instance_manager: StrategyInstanceManager, node_id: int = 1):
        flogger.info("strategy caller start init", node_id=node_id)
        self.front_api = front_api
        self.next_request_id = 1
        self.node_id = node_id
        self.strategy_instance_manager = strategy_instance_manager
        self.id_generator = IdGenerator.get_instance()
        flogger.debug("strategy caller configuration",
                      node_id=node_id, initial_request_id=self.next_request_id)
        flogger.info("strategy caller end init", node_id=node_id)

    def insert_order(self, strategy_instance_id: int, order: FbOrderEntity) -> int:
        order_id = self.id_generator.get_order_id(self.node_id)
        request_id = self.next_request_id
        self.next_request_id += 1
        try:
            trading_account_id = self.strategy_instance_manager.get_strategy_instance_trading_account_id(
                strategy_instance_id)
            order = convert_model(order, OrderEntity, order_id=order_id, priority=0,
                                  trading_account_id=trading_account_id, order_source=OrderSourceEnum.ORDER_SOURCE_STRATEGY_ORDER.value)
            print(order)
            self.front_api.create_order(order, int(request_id))
            flogger.info("order sent", strategy_instance_id=strategy_instance_id,
                         order_id=order_id, request_id=request_id, send_status="success")
            return order_id
        except Exception as e:
            flogger.error("order send failed",
                          strategy_instance_id=strategy_instance_id, order_id=-1, error=str(e))
            return -1

    def cancel_order(self, strategy_instance_id: int, order: FbCancelOrderEntity) -> bool:
        request_id = self.next_request_id
        self.next_request_id += 1
        try:
            trading_account_id = self.strategy_instance_manager.get_strategy_instance_trading_account_id(
                strategy_instance_id)
            order = convert_model(order, OrderEntity, direction=DirectionEnum.DIRECTION_BUY.value, offset_flag=OffsetFlagEnum.OFFSET_FLAG_AUTO.value, hedge_flag=HedgeFlagEnum.HEDGE_FLAG_ARBITRAGE.value, price=0.00, volume=0, price_category=PriceCategoryEnum.PRICE_CATEGORY_OTHER.value,
                                  time_condition=TimeConditionEnum.TIME_CONDITION_GFD.value, volume_condition=VolumeConditionEnum.VOLUME_ANY.value, portfolio_id=0, priority=0, custom_flag='', seat_no='', trading_account_id=trading_account_id, investor_id='', order_source=OrderSourceEnum.ORDER_SOURCE_STRATEGY_ORDER.value)
            print(order)
            self.front_api.cancel_order(order, request_id)
            flogger.info("order canceled", strategy_instance_id=strategy_instance_id, instrument_id=order.instrument_id,
                         order_id=order.order_id, request_id=request_id, send_status="success")
            return True
        except Exception as e:
            flogger.error("order cancel failed", strategy_instance_id=strategy_instance_id, instrument_id=order.instrument_id,
                          order_id=order.order_id, error=str(e))
            return False

    def modify_instrument_param_value(self, strategy_instance_id: int, instrument_param_value: FbInstrumentParamValueEntity) -> bool:
        request_id = self.next_request_id
        self.next_request_id += 1
        try:
            trading_account_id = self.strategy_instance_manager.get_strategy_instance_trading_account_id(
                strategy_instance_id)
            instrument_param_value = convert_model(instrument_param_value, InstrumentParamValueEntity, trading_account_id=trading_account_id,
                                                   last_operator_id=0, last_operate_source=LastOperateSourceEnum.STRATEGY.value, status=IntStatusEnum.ACTIVE.value)
            print(instrument_param_value)
            self.front_api.modify_instrument_param_value(
                instrument_param_value, request_id)
            flogger.info("instrument param modify",
                         strategy_instance_id=strategy_instance_id,
                         instrument_id=instrument_param_value.instrument_id,
                         param_key=instrument_param_value.param_key,
                         param_value=instrument_param_value.param_value,
                         request_id=request_id,
                         send_status="success")
            return True
        except Exception as e:
            flogger.error("instrument param modification failed",
                          strategy_instance_id=strategy_instance_id,
                          instrument_id=instrument_param_value.instrument_id,
                          error=str(e))
            return False

    def modify_custom_param_value(self, strategy_instance_id: int, custom_param_value: FbCustomParamValueEntity) -> bool:
        request_id = self.next_request_id
        self.next_request_id += 1
        try:
            trading_account_id = self.strategy_instance_manager.get_strategy_instance_trading_account_id(
                strategy_instance_id)
            custom_param_value = convert_model(custom_param_value, CustomParamValueEntity, trading_account_id=trading_account_id,
                                               last_operator_id=0, last_operate_source=LastOperateSourceEnum.STRATEGY.value, status=IntStatusEnum.ACTIVE.value)
            print(custom_param_value)
            self.front_api.modify_custom_param_value(
                custom_param_value, request_id)
            flogger.info("custom param modify",
                         strategy_instance_id=strategy_instance_id,
                         custom_id=custom_param_value.custom_id,
                         param_key=custom_param_value.param_key,
                         param_value=custom_param_value.param_value,
                         request_id=request_id,
                         send_status="success")
            return True
        except Exception as e:
            flogger.error("custom param modification failed",
                          strategy_instance_id=strategy_instance_id,
                          custom_id=custom_param_value.custom_id,
                          error=str(e))
            return False
