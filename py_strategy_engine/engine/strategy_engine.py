from typing import Dict, Any, List, Union, Callable
from common.strategy_manager import StrategyManager
from common.strategy_instance_manager import StrategyInstanceManager
from common.event import EventEngine, Event
from common.front_api import FrontApi
from common.mdb import MemoryDatabase
from common.constants import *
from common.table_enum import table_enum as common_table_enum
from common.table_manager import init_tables
from engine.strategy_factory import StrategyFactory
from engine.strategy_stream import StrategyStream
from engine.strategy_caller import StrategyCaller
from web.http_server import HttpServer
from py_strategy_api.fb_models import *
from py_strategy_api import fb_enum as api_enum
from config.config import Config
from utils.flog import flogger
from utils.strategy_status_transition import valid_status_transition


class StrategyEngine:
    """策略引擎，管理策略的生命周期和执行"""

    def __init__(self, event_engine: EventEngine, http_server: HttpServer, mdb: MemoryDatabase, strategy_manager: StrategyManager, strategy_instance_manager: StrategyInstanceManager, node_id: int):
        flogger.info("strategy engine start init")
        self.event_engine = event_engine
        self.http_server = http_server
        self.strategy_manager = strategy_manager
        self.strategy_instance_manager = strategy_instance_manager
        self.node_id = node_id
        self.mdb = mdb
        self.front_api = FrontApi(event_engine)
        self.strategy_factory = StrategyFactory(
            self, self.strategy_manager, self.strategy_instance_manager, self.node_id)
        self.strategy_stream = StrategyStream(
            event_engine, self.mdb, self.strategy_instance_manager)
        self.strategy_caller = StrategyCaller(
            self.front_api, self.strategy_instance_manager, self.node_id)

        self._init_tables()
        self._register_event_handlers()
        self.strategy_factory.load_all()
        flogger.info("strategy engine end init", node_id=self.node_id)

    def _init_tables(self) -> None:
        """初始化内存数据库表格"""
        flogger.info("init mdb tables")
        init_tables(self.mdb)

    def _register_event_handlers(self) -> None:
        """注册事件处理函数"""
        # 策略管理事件
        flogger.info("register event handlers")
        self.event_engine.register(
            EVENT_LOAD_STRATEGY, self._process_load_strategy)
        self.event_engine.register(
            EVENT_CREATE_STRATEGY_INSTANCE, self._process_create_strategy_instance)
        self.event_engine.register(
            EVENT_START_STRATEGY_INSTANCE, self._process_start_strategy_instance)
        self.event_engine.register(
            EVENT_STOP_STRATEGY_INSTANCE, self._process_stop_strategy_instance)
        self.event_engine.register(
            EVENT_DELETE_STRATEGY_INSTANCE, self._process_delete_strategy_instance)
        self.event_engine.register(
            EVENT_UPDATE_STRATEGY_INSTANCE_PARAM, self._process_update_strategy_instance_param)
        self.event_engine.register(
            EVENT_MODIFY_INSTANCE_PRIORITY, self._process_modify_instance_priority)

    def start(self) -> None:
        flogger.info("starting strategy engine")
        self._init_front_api()
        flogger.info("strategy engine started", node_id=self.node_id)

    def stop(self) -> None:
        flogger.info("stopping strategy engine")

        # 1. 首先停止front api，阻止新的市场数据和其他事件产生
        try:
            flogger.info("stopping front api to prevent new events")
            self.front_api.stop()
            flogger.info("front api stopped")
        except Exception as e:
            flogger.error("front api stop failed", error=str(e))

        # 2. 停止所有策略实例
        strategty_instance_list = self.strategy_instance_manager.get_strategy_instance_id_list()
        flogger.info("stop all strategy instances",
                     strategty_instance_list=strategty_instance_list)
        for strategy_instance_id in strategty_instance_list:
            self.stop_strategy_instance(strategy_instance_id)

        flogger.info("strategy engine stopped", node_id=self.node_id)

    def _init_front_api(self) -> None:
        flogger.debug("front api start init")
        self.front_api.sub(common_table_enum.user, 500)
        self.front_api.sub(common_table_enum.order, 500)
        self.front_api.sub(common_table_enum.market_data, 1000)
        self.front_api.sub(common_table_enum.trade, 500)
        self.front_api.sub(common_table_enum.position, 500)
        self.front_api.sub(common_table_enum.security_instrument, 500)
        self.front_api.sub(common_table_enum.future_instrument, 500)
        self.front_api.sub(common_table_enum.option_instrument, 500)
        self.front_api.sub(common_table_enum.instrument_param_value, 500)
        self.front_api.sub(common_table_enum.custom_param_value, 500)
        self.front_api.sub(common_table_enum.trading_account, 500)

        front_api_config = Config.get_instance().get_front_api_config()
        flogger.info("front api config", pub_address=front_api_config.pub_address, req_address=front_api_config.req_address,
                     log_level=front_api_config.log_level, log_dir=front_api_config.log_dir)
        self.front_api.connect(
            front_api_config.pub_address,
            front_api_config.req_address,
            front_api_config.log_level,
            front_api_config.log_dir
        )
        flogger.info("front api end init")

    def _process_load_strategy(self, event: Event) -> None:
        """处理加载策略事件"""

        data: Dict[str, Any] = event.data
        file_path = data.get("file_path")
        request_id = data.get("request_id")
        flogger.debug("process load strategy", file_path=file_path)
        if not file_path:
            flogger.error("missing strategy file path", request_id=request_id)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "missing strategy file path")
            return

        success = self.strategy_factory.load_strategy_from_file(file_path)
        if success:
            flogger.info("strategy load", file_path=file_path,
                         request_id=request_id)
        else:
            flogger.error("strategy load failed",
                          file_path=file_path, request_id=request_id)
        if request_id:
            self.http_server.send_response_sync(
                request_id, success,
                f"strategy load {file_path} {'success' if success else 'failed'}"
            )

    def _process_create_strategy_instance(self, event: Event) -> None:
        """处理创建策略事件"""
        data = event.data
        strategy_name = data.get("strategy_name")
        strategy_instance_name = data.get("strategy_instance_name")
        user_id = data.get("user_id")
        params = data.get("params", {})
        trading_account_id = data.get("trading_account_id")
        request_id = data.get("request_id")
        flogger.debug("process create strategy", strategy_name=strategy_name, strategy_instance_name=strategy_instance_name,
                      request_id=request_id, params=params, trading_account_id=trading_account_id, user_id=user_id)
        if not strategy_name:
            flogger.error("missing strategy name", request_id=request_id)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "missing strategy name")
            return
        strategy_instance_id = self.strategy_factory.create_strategy_instance(
            strategy_name, strategy_instance_name, trading_account_id, user_id, params)
        success = False
        if strategy_instance_id and self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            success = self.init_strategy_instance(strategy_instance_id)
        if success:
            flogger.info("create strategy instance", strategy_instance_id=strategy_instance_id,
                         strategy_name=strategy_name)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, True, "create strategy success", {
                        "strategy_instance_id": strategy_instance_id}
                )
        else:
            flogger.error("create strategy failed")
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "create strategy failed")

    def _process_start_strategy_instance(self, event: Event) -> None:
        data = event.data
        strategy_instance_id = data.get("strategy_instance_id")
        request_id = data.get("request_id")
        flogger.debug("process start strategy instance",
                      strategy_instance_id=strategy_instance_id, request_id=request_id)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error("strategy instance not found",
                          strategy_instance_id=strategy_instance_id, request_id=request_id)
            if request_id:
                self.http_server.send_response_sync(request_id, False, "strategy instance not found", {
                                                    "strategy_instance_id": strategy_instance_id})
            return
        current_strategy_status = self.strategy_instance_manager.get_strategy_instance_status(
            strategy_instance_id)
        if not valid_status_transition(strategy_instance_id, current_strategy_status, StrategyStateEnum.STRATEGY_RUNNING_STAT.value):
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "invalid state change", {"strategy_instance_id": strategy_instance_id, "current_status": current_strategy_status})
            return
        success = self.start_strategy_instance(strategy_instance_id)
        flogger.info("strategy instance start operation completed",
                     strategy_instance_id=strategy_instance_id, success=success)
        if request_id:
            self.http_server.send_response_sync(
                request_id, success,
                f"strategy instance start {'successful' if success else 'failed'}", {
                    "strategy_instance_id": strategy_instance_id}
            )

    def _process_stop_strategy_instance(self, event: Event) -> None:
        """处理停止策略事件"""
        data = event.data
        strategy_instance_id = data.get("strategy_instance_id")
        request_id = data.get("request_id")
        flogger.debug("process stop strategy instance",
                      strategy_instance_id=strategy_instance_id, request_id=request_id)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error("strategy instance not found",
                          strategy_instance_id=strategy_instance_id, request_id=request_id)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "strategy instance not found", {"strategy_instance_id": strategy_instance_id})
            return
        current_strategy_status = self.strategy_instance_manager.get_strategy_instance_status(
            strategy_instance_id)
        if not valid_status_transition(strategy_instance_id, current_strategy_status, StrategyStateEnum.STRATEGY_PAUSE_STAT.value):
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "invalid state change", {"strategy_instance_id": strategy_instance_id, "current_status": current_strategy_status})
            return
        success = self.stop_strategy_instance(strategy_instance_id)
        flogger.info("strategy instance stop operation completed",
                     strategy_instance_id=strategy_instance_id, success=success)
        if request_id:
            self.http_server.send_response_sync(
                request_id, success,
                f"strategy instance stop {'successful' if success else 'failed'}", {
                    "strategy_instance_id": strategy_instance_id}
            )

    def _process_delete_strategy_instance(self, event: Event) -> None:
        """处理删除策略事件"""
        data = event.data
        strategy_instance_id = data.get("strategy_instance_id")
        request_id = data.get("request_id")
        flogger.debug("process delete strategy instance",
                      strategy_instance_id=strategy_instance_id, request_id=request_id)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error("strategy instance not found",
                          strategy_instance_id=strategy_instance_id, request_id=request_id)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"strategy instance {strategy_instance_id} not found")
            return
        current_strategy_status = self.strategy_instance_manager.get_strategy_instance_status(
            strategy_instance_id)
        if not valid_status_transition(strategy_instance_id, current_strategy_status, StrategyStateEnum.STRATEGY_DELETE_STAT.value):
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, "invalid state change", {"strategy_instance_id": strategy_instance_id, "current_status": current_strategy_status})
            return
        success = self.delete_strategy_instance(strategy_instance_id)
        flogger.info("strategy instance delete operation completed",
                     strategy_instance_id=strategy_instance_id, success=success)
        if request_id:
            self.http_server.send_response_sync(
                request_id, success,
                f"strategy instance delete {'successful' if success else 'failed'}"
            )

    def _process_update_strategy_instance_param(self, event: Event) -> None:
        """处理更新策略参数事件"""
        data = event.data
        strategy_instance_id = data.get("strategy_instance_id")
        param_name = data.get("param_name")
        param_value = data.get("param_value")
        request_id = data.get("request_id")
        flogger.info(f"process update strategy instance param", strategy_instance_id=strategy_instance_id,
                     param_name=param_name, param_value=param_value, request_id=request_id)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error("strategy not found for parameter update",
                          strategy_instance_id=strategy_instance_id)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"strategy instance {strategy_instance_id} not found")
            return
        current_strategy_status = self.strategy_instance_manager.get_strategy_instance_status(
            strategy_instance_id)
        if current_strategy_status not in [StrategyStateEnum.STRATEGY_INIT_STAT.value, StrategyStateEnum.STRATEGY_PAUSE_STAT.value]:
            flogger.warning("strategy instance not pause",
                            strategy_instance_id=strategy_instance_id, current_strategy_status=current_strategy_status)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"strategy instance {strategy_instance_id} not pause")
            return
        success = self.strategy_instance_manager.update_strategy_parameter(
            strategy_instance_id, param_name, param_value)
        if success:
            flogger.info("strategy instance parameter updated", strategy_instance_id=strategy_instance_id,
                         param_name=param_name, param_value=param_value)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, True,
                    "strategy instance parameter update successful",
                    {"strategy_instance_id": strategy_instance_id, "param_name": param_name,
                        "param_value": param_value}
                )
        else:
            flogger.error("strategy instance parameter update failed",
                          strategy_instance_id=strategy_instance_id, param_name=param_name)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"strategy instance parameter update failed")

    def _process_modify_instance_priority(self, event: Event) -> None:
        """处理修改策略实例优先级事件"""
        data = event.data
        strategy_instance_id = data.get("strategy_instance_id")
        priority = data.get("priority")
        strategy_instance_name = data.get("strategy_instance_name")
        request_id = data.get("request_id")

        flogger.debug("process modify instance priority",
                      strategy_instance_id=strategy_instance_id,
                      priority=priority,
                      strategy_instance_name=strategy_instance_name,
                      request_id=request_id)

        # 检查策略实例是否存在
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error("strategy instance not found",
                          strategy_instance_id=strategy_instance_id, request_id=request_id)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"strategy instance {strategy_instance_id} not found")
            return

        # 检查策略实例状态，只有非运行状态才能修改优先级
        current_strategy_status = self.strategy_instance_manager.get_strategy_instance_status(
            strategy_instance_id)
        if current_strategy_status == StrategyStateEnum.STRATEGY_RUNNING_STAT.value:
            flogger.warning("cannot modify priority for running strategy instance",
                            strategy_instance_id=strategy_instance_id,
                            current_strategy_status=current_strategy_status)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"cannot modify priority for running strategy instance {strategy_instance_id}")
            return

        # 调用修改优先级方法
        success = self.modify_instance_priority(
            strategy_instance_id, priority, strategy_instance_name)
        if success:
            flogger.info("strategy instance priority modified successfully",
                         strategy_instance_id=strategy_instance_id,
                         priority=priority, strategy_instance_name=strategy_instance_name)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, True,
                    "strategy instance priority modified successfully",
                    {"strategy_instance_id": strategy_instance_id, "priority": priority,
                     "strategy_instance_name": strategy_instance_name}
                )
        else:
            flogger.error("strategy instance priority modification failed",
                          strategy_instance_id=strategy_instance_id, priority=priority)
            if request_id:
                self.http_server.send_response_sync(
                    request_id, False, f"strategy instance priority modification failed")

    def init_strategy_instance(self, strategy_instance_id: int) -> bool:
        """初始化策略实例"""
        flogger.debug("initializing strategy instance",
                      strategy_instance_id=strategy_instance_id)
        strategy_instance_name = self.strategy_instance_manager.get_strategy_instance_name(
            strategy_instance_id)
        strategy_instance_api = self.strategy_instance_manager.get_strategy_instance_api(
            strategy_instance_id)
        flogger.debug("calling strategy on_init",
                      strategy_instance_id=strategy_instance_id, strategy_instance_name=strategy_instance_name)
        self.call_strategy_func(strategy_instance_id,
                                strategy_instance_api.on_init)
        self._update_strategy_instance_status(
            strategy_instance_id, StrategyStateEnum.STRATEGY_INIT_STAT.value)
        flogger.info("strategy instance init", strategy_instance_id=strategy_instance_id,
                     strategy_instance_name=strategy_instance_name)
        return True

    def start_strategy_instance(self, strategy_instance_id: int) -> bool:
        """启动策略实例"""
        flogger.debug("starting strategy instance",
                      strategy_instance_id=strategy_instance_id)
        strategy_instance_api = self.strategy_instance_manager.get_strategy_instance_api(
            strategy_instance_id)
        strategy_instance_name = self.strategy_instance_manager.get_strategy_instance_name(
            strategy_instance_id)
        flogger.debug("calling strategy on_start",
                      strategy_instance_id=strategy_instance_id, strategy_instance_name=strategy_instance_name)
        self.call_strategy_func(strategy_instance_id,
                                strategy_instance_api.on_start)
        self._update_strategy_instance_status(
            strategy_instance_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value)
        flogger.info("strategy instance start", strategy_instance_id=strategy_instance_id,
                     strategy_instance_name=strategy_instance_name)
        return True

    def stop_strategy_instance(self, strategy_instance_id: int) -> bool:
        """停止策略实例"""
        flogger.debug("stopping strategy instance",
                      strategy_instance_id=strategy_instance_id)
        strategy_instance_api = self.strategy_instance_manager.get_strategy_instance_api(
            strategy_instance_id)
        strategy_instance_name = self.strategy_instance_manager.get_strategy_instance_name(
            strategy_instance_id)
        flogger.debug("calling strategy on_stop",
                      strategy_instance_id=strategy_instance_id, strategy_instance_name=strategy_instance_name)
        self.call_strategy_func(strategy_instance_id,
                                strategy_instance_api.on_stop)
        self._update_strategy_instance_status(
            strategy_instance_id, StrategyStateEnum.STRATEGY_PAUSE_STAT.value)
        flogger.info("strategy instance stopped", strategy_instance_id=strategy_instance_id,
                     strategy_instance_name=strategy_instance_name)
        return True

    def delete_strategy_instance(self, strategy_instance_id: int) -> bool:
        """删除策略实例"""
        flogger.debug("deleting strategy instance ",
                      strategy_instance_id=strategy_instance_id)
        strategy_instance_api = self.strategy_instance_manager.get_strategy_instance_api(
            strategy_instance_id)
        strategy_name = self.strategy_instance_manager.get_strategy_instance_name(
            strategy_instance_id)
        flogger.debug("calling strategy on_stop before deletion",
                      strategy_instance_id=strategy_instance_id, strategy_name=strategy_name)
        self.call_strategy_func(strategy_instance_id,
                                strategy_instance_api.on_stop)
        flogger.debug("removing strategy from strategy manager",
                      strategy_instance_id=strategy_instance_id)
        success = self.strategy_instance_manager.delete_strategy_instance(
            strategy_instance_id)
        if not success:
            flogger.error("strategy instance deletion failed",
                          strategy_instance_id=strategy_instance_id, strategy_name=strategy_name)
            return False
        self._update_strategy_instance_status(
            strategy_instance_id, StrategyStateEnum.STRATEGY_DELETE_STAT.value)
        flogger.info("strategy instance deleted", strategy_instance_id=strategy_instance_id,
                     strategy_name=strategy_name)
        return True

    def modify_instance_priority(self, strategy_instance_id: int, priority: int, strategy_instance_name: str) -> bool:
        """修改策略实例优先级"""
        flogger.debug("modifying strategy instance priority",
                      strategy_instance_id=strategy_instance_id,
                      priority=priority,
                      strategy_instance_name=strategy_instance_name)

        success = self.strategy_instance_manager.modify_instance_priority(
            strategy_instance_id, priority, strategy_instance_name)

        if success:
            flogger.info("strategy instance priority modified successfully",
                         strategy_instance_id=strategy_instance_id,
                         priority=priority,
                         strategy_instance_name=strategy_instance_name)
        else:
            flogger.error("strategy instance priority modification failed",
                          strategy_instance_id=strategy_instance_id,
                          priority=priority,
                          strategy_instance_name=strategy_instance_name)

        return success

    def _update_strategy_instance_status(self, strategy_instance_id: int, status: str) -> None:
        flogger.debug("updating strategy instance status",
                      strategy_instance_id=strategy_instance_id, status=status)
        self.strategy_instance_manager.update_strategy_status(
            strategy_instance_id, status)
        flogger.info("strategy instance status updated",
                     strategy_instance_id=strategy_instance_id, status=status)

    def insert_order(self, strategy_instance_id: int, order: FbOrderEntity) -> int:
        """发送订单"""
        flogger.debug("sending order for strategy instance",
                      strategy_instance_id=strategy_instance_id)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error(
                "strategy instance not found for order sending", strategy_instance_id=strategy_instance_id)
            return 0
        current_strategy_status = self.strategy_instance_manager.get_strategy_instance_status(
            strategy_instance_id)
        if current_strategy_status != StrategyStateEnum.STRATEGY_RUNNING_STAT.value:
            flogger.error(
                "strategy instance not running for order sending", strategy_instance_id=strategy_instance_id)
        return self.strategy_caller.insert_order(strategy_instance_id, order)

    def cancel_order(self, strategy_instance_id: int, order: FbCancelOrderEntity) -> bool:
        """撤销订单"""
        flogger.debug("canceling order for strategy instance",
                      strategy_instance_id=strategy_instance_id, order_id=order.order_id)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error(
                "strategy instance not found for order cancellation", strategy_instance_id=strategy_instance_id)
            return False
        return self.strategy_caller.cancel_order(strategy_instance_id, order)

    def modify_instrument_param_value(self, strategy_instance_id: int, instrument_param_value: FbInstrumentParamValueEntity) -> bool:
        """修改合约参数"""
        flogger.debug("modifying instrument param from strategy instance", strategy_instance_id=strategy_instance_id,
                      insrument_id=instrument_param_value.instrument_id, param_key=instrument_param_value.param_key, param_value=instrument_param_value.param_value)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error(
                "strategy instance not found for instrument param modification", strategy_instance_id=strategy_instance_id)
            return False
        return self.strategy_caller.modify_instrument_param_value(strategy_instance_id, instrument_param_value)

    def modify_custom_param_value(self, strategy_instance_id: int, custom_param_value: FbCustomParamValueEntity) -> bool:
        """修改自定义参数"""
        flogger.debug("modifying custom param from strategy instance", strategy_instance_id=strategy_instance_id,
                      custom_id=custom_param_value.custom_id, param_key=custom_param_value.param_key, param_value=custom_param_value.param_value)
        if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
            flogger.error(
                "strategy instance not found for custom param modification", strategy_instance_id=strategy_instance_id)
            return False
        return self.strategy_caller.modify_custom_param_value(strategy_instance_id, custom_param_value)

    def call_strategy_func(self, strategy_instance_id: int, func: Callable, params: Any = None) -> None:
        func_name = func.__name__
        flogger.debug("calling strategy instance function", strategy_instance_id=strategy_instance_id,
                      function_name=func_name, has_params=params is not None)
        try:
            if params is not None:
                func(params)
            else:
                func()
            flogger.debug("strategy instance function call completed",
                          strategy_instance_id=strategy_instance_id, function_name=func_name)
        except Exception as e:
            self.strategy_instance_manager.update_strategy_status(
                strategy_instance_id, StrategyStateEnum.STRATEGY_PAUSE_STAT.value)

            import traceback
            error_msg = traceback.format_exc()
            flogger.error("strategy instance function call failed",
                          strategy_instance_id=strategy_instance_id,
                          function_name=func_name,
                          error=str(e),
                          traceback=error_msg)

    def get_from_mdb(self, strategy_instance_id: int, table: api_enum.table_enum, **kwargs) -> Union[Any, List[Any], None]:
        flogger.info("get from mdb", strategy_instance_id=strategy_instance_id,
                     table=table.name, params=kwargs or None)
        convert_table = self._convert_table_enum(table)
        if not convert_table:
            flogger.debug("empty convert table", table=table.name)
            return None
        try:
            if not self.strategy_instance_manager.check_strategy_instance_exists(strategy_instance_id):
                flogger.warning("strategy instance not found",
                                strategy_instance_id=strategy_instance_id)
                return None
            if kwargs:
                return self.mdb.get_records_by_params(convert_table, **kwargs)
            else:
                return self.mdb.get_all_records(convert_table)
        except Exception as e:
            flogger.error("query failed", strategy_instance_id=strategy_instance_id,
                          table=table.name, params=kwargs, error=str(e))
            raise ValueError(f"query failed: {str(e)}")

    def _convert_table_enum(self, table: api_enum.table_enum) -> common_table_enum:
        flogger.debug("convert table enum",
                      table_name=table.name, table_id=table.value)
        if not hasattr(table, 'value'):
            return None
        for common_enum_item in common_table_enum:
            if common_enum_item.value == table.value:
                flogger.debug("convert table enum success",
                              table_name=table.name, table_id=table.value)
                return common_enum_item
        return None
