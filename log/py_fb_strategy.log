ts=2025-07-22T15:37:19.565212 level=info event="init struct flogger" module=config lineno=102
ts=2025-07-22T15:37:19.565373 level=info event="init struct flogger" module=config lineno=102
ts=2025-07-23T09:30:10.210575 level=info event="init struct flogger" module=config lineno=102
ts=2025-07-23T09:30:10.210725 level=info event="strategy factory end init" module=strategy_factory lineno=59
ts=2025-07-23T09:30:10.210750 level=info event="loading strategy from file" module=strategy_factory lineno=108 file_path=./strategies/my_trading_strategy.py
ts=2025-07-23T09:30:10.211286 level=info event="strategy loaded" module=strategy_factory lineno=151 class_name=MyTradingStrategyClass file_path=./strategies/my_trading_strategy.py strategy_name=my_trading_strategy
ts=2025-07-23T09:30:10.211309 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=param1 strategy_name=my_trading_strategy
ts=2025-07-23T09:30:10.211325 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=param2 strategy_name=my_trading_strategy
ts=2025-07-23T09:30:10.211339 level=info event="strategy parameters analysis completed" module=strategy_manager lineno=204 param_count=2 strategy_name=my_trading_strategy
ts=2025-07-23T09:30:10.211570 level=info event="strategy deploy info added" module=strategy_manager lineno=69 engine_id=1 last_operator_id=0 strategy_name=my_trading_strategy strategy_version=1.0.0
ts=2025-07-23T09:30:10.211588 level=info event="strategy load" module=strategy_factory lineno=158 class_name=MyTradingStrategyClass strategy_name=my_trading_strategy version=1.0.0
ts=2025-07-23T09:30:10.211613 level=info event="loading strategy from file" module=strategy_factory lineno=108 file_path=./strategies/arbitrage_bot.py
ts=2025-07-23T09:30:10.211938 level=info event="strategy loaded" module=strategy_factory lineno=151 class_name=ArbitrageBotImplementation file_path=./strategies/arbitrage_bot.py strategy_name=arbitrage_bot
ts=2025-07-23T09:30:10.211959 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=threshold strategy_name=arbitrage_bot
ts=2025-07-23T09:30:10.211974 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=max_position strategy_name=arbitrage_bot
ts=2025-07-23T09:30:10.211989 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=instruments strategy_name=arbitrage_bot
ts=2025-07-23T09:30:10.212003 level=info event="strategy parameters analysis completed" module=strategy_manager lineno=204 param_count=3 strategy_name=arbitrage_bot
ts=2025-07-23T09:30:10.212023 level=info event="strategy deploy info added" module=strategy_manager lineno=69 engine_id=1 last_operator_id=0 strategy_name=arbitrage_bot strategy_version=2.1.0
ts=2025-07-23T09:30:10.212040 level=info event="strategy load" module=strategy_factory lineno=158 class_name=ArbitrageBotImplementation strategy_name=arbitrage_bot version=2.1.0
ts=2025-07-23T09:53:24.007225 level=info event="init struct flogger" module=config lineno=102
ts=2025-07-23T09:53:24.007364 level=info event="strategy factory end init" module=strategy_factory lineno=59
ts=2025-07-23T09:53:24.007388 level=info event="loading strategy from file" module=strategy_factory lineno=108 file_path=./strategies/demo_strategy_with_external_module.py
ts=2025-07-23T09:53:24.008097 level=info event="strategy loaded" module=strategy_factory lineno=151 class_name=DemoStrategyWithExternalModule file_path=./strategies/demo_strategy_with_external_module.py strategy_name=demo_strategy_with_external_module
ts=2025-07-23T09:53:24.008121 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=trade_threshold strategy_name=demo_strategy_with_external_module
ts=2025-07-23T09:53:24.008139 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=max_position strategy_name=demo_strategy_with_external_module
ts=2025-07-23T09:53:24.008158 level=warning event="parameter not found in strategy class" module=strategy_manager lineno=200 param_name=log_level strategy_name=demo_strategy_with_external_module
ts=2025-07-23T09:53:24.008172 level=info event="strategy parameters analysis completed" module=strategy_manager lineno=204 param_count=3 strategy_name=demo_strategy_with_external_module
ts=2025-07-23T09:53:24.008270 level=info event="strategy deploy info added" module=strategy_manager lineno=69 engine_id=1 last_operator_id=0 strategy_name=demo_strategy_with_external_module strategy_version=1.0.0
ts=2025-07-23T09:53:24.008288 level=info event="strategy load" module=strategy_factory lineno=158 class_name=DemoStrategyWithExternalModule strategy_name=demo_strategy_with_external_module version=1.0.0
