import signal
import sys
import time
import threading
import asyncio
import queue
from typing import Optional, Any, List
from fastapi import FastAPI, WebSocket, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from strategy_mid_server.mid_server import StrategyMidServer
from common.mdb import MemoryDatabase
from common.models import *
from utils.flog import flogger
from utils.csv_converter import CSVConverter
from config.config import ConfigManager


class BackgroundTaskManager:
    """后台任务管理器"""
    
    def __init__(self):
        self.task_queue = queue.Queue()
        self.worker_thread = None
        self.running = False
        self.loop = None
        
    def start(self):
        """启动后台任务处理器"""
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        flogger.info("background task manager started")
        
    def stop(self):
        """停止后台任务处理器"""
        self.running = False
        # 添加一个停止信号到队列
        self.task_queue.put(None)
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
        flogger.info("background task manager stopped")
        
    def submit_task(self, coro, task_name: str = "unknown"):
        """提交异步任务到后台执行"""
        if self.running:
            self.task_queue.put((coro, task_name))
            flogger.debug("task submitted to background", task_name=task_name)
        else:
            flogger.warning("background task manager not running, task ignored", task_name=task_name)
            
    def _worker(self):
        """后台工作线程"""
        flogger.info("background task worker started")
        
        # 为这个线程创建新的事件循环
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            while self.running:
                try:
                    # 从队列获取任务，超时1秒
                    task_item = self.task_queue.get(timeout=1)
                    
                    # 检查停止信号
                    if task_item is None:
                        break
                        
                    coro, task_name = task_item
                    
                    # 执行异步任务
                    try:
                        self.loop.run_until_complete(coro)
                        flogger.debug("background task completed", task_name=task_name)
                    except Exception as e:
                        flogger.error("background task failed", task_name=task_name, error=str(e))
                    finally:
                        self.task_queue.task_done()
                        
                except queue.Empty:
                    # 超时，继续循环
                    continue
                except Exception as e:
                    flogger.error("background task worker error", error=str(e))
                    
        finally:
            # 清理事件循环
            try:
                self.loop.close()
            except Exception as e:
                flogger.error("failed to close background event loop", error=str(e))
                
        flogger.info("background task worker stopped")


class HttpServer:
    """HTTP服务器类，运行在独立线程中"""
    
    def __init__(self, app: FastAPI, host: str, port: int):
        self.app = app
        self.host = host
        self.port = port
        self.thread = None
        
    def start(self):
        flogger.info("starting http server", host=self.host, port=self.port)
        self.thread = threading.Thread(target=self._run, name="HttpServer")
        self.thread.daemon = True
        self.thread.start()
        flogger.info("http server started", host=self.host, port=self.port)
        
    def _run(self):
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info",
            access_log=False
        )
        
    def stop(self):
        flogger.info("http server stopped")


class WebSocketServer:
    """WebSocket服务器类，运行在独立线程中"""
    
    def __init__(self, app: FastAPI, host: str, port: int):
        self.app = app
        self.host = host
        self.port = port
        self.thread = None
        
    def start(self):
        flogger.info("starting websocket server", host=self.host, port=self.port)
        self.thread = threading.Thread(target=self._run, name="WebSocketServer")
        self.thread.daemon = True
        self.thread.start()
        flogger.info("websocket server started", host=self.host, port=self.port)
        
    def _run(self):
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info",
            access_log=False
        )
        
    def stop(self):
        flogger.info("websocket server stopped")


class Main:
    """策略中台主应用类"""

    def __init__(self):
        config_file = "../config/py_strategy_mid_config.json"
        self.config = ConfigManager.get_instance(config_file).get_config()
        flogger.info("init py strategy mid server", config=self.config)
        
        # 组件初始化
        self.mdb = MemoryDatabase()
        self.mid_server: Optional[StrategyMidServer] = None
        self.app: Optional[FastAPI] = None
        self.http_server: Optional[HttpServer] = None
        self.ws_server: Optional[WebSocketServer] = None
        self.running = False
        
        # 后台任务管理器
        self.background_task_manager = BackgroundTaskManager()

        # 设置应用
        self._setup_app()
        flogger.info("py strategy mid server init complete")

    def _check_mid_server_ready(self) -> Optional[HttpResponseModel]:
        if not self.mid_server:
            return HttpResponseModel(
                data=[],
                code=-1,
                message="mid server not initialized"
            )
        return None

    def _setup_app(self):
        """设置FastAPI应用"""
        self.app = FastAPI(
            title="py_strategy_mid",
            description="manage python strategy",
            version="1.0.0"
        )

        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self._setup_routes()

    def _setup_routes(self):
        """设置所有路由"""
        @self.app.get("/heartbeat")
        async def heartbeat():
            """心跳检查"""
            if self.mid_server and self.running:
                return HeartbeatResponseModel(code=0, message="success")
            else:
                return HeartbeatResponseModel(code=-1, message="failed")

        @self.app.post("/py_strategy/create_instance", response_model=HttpResponseModel)
        async def create_strategy_instance(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("create strategy instance request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                strategy_instance = CSVConverter.parse_csv_to_model(request_data, StrategyInstanceMsg)
                result = await self.mid_server.handle_create_strategy_instance(strategy_instance)
                
                # 关键改动：使用后台任务执行WebSocket推送
                if result.code == 0:
                    # 提交后台任务，不等待完成
                    publish_task = self.mid_server.publish_strategy_instance(strategy_instance.strategy_instance_id)
                    self.background_task_manager.submit_task(
                        publish_task, 
                        f"publish_strategy_instance_{strategy_instance.strategy_instance_id}"
                    )
                    
                return result
            except Exception as e:
                flogger.error("create strategy instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"create instance failed: {str(e)}"
                )

        @self.app.post("/py_strategy/update_instance_param", response_model=HttpResponseModel)
        async def update_instance_param(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("update instance param request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                params: List[StrategyInstanceParamMsg] = []
                lines = request_data.strip().split('\n')
                if not lines:
                    return HttpResponseModel(
                        data=[],
                        code=-1,
                        message="empty message"
                    )
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        param = CSVConverter.parse_csv_to_model(line, StrategyInstanceParamMsg)
                        params.append(param)
                    except Exception as e:
                        flogger.error("parse csv line failed",
                                     line_num=line_num,
                                     line_data=line,
                                     error=str(e))
                        return HttpResponseModel(
                            data=[],
                            code=-1,
                            message=f"parse csv line {line_num} failed: {str(e)}"
                        )
                result = await self.mid_server.handle_update_instance_param(params)
                
                # 关键改动：使用后台任务执行WebSocket推送
                if result.code == 0:
                    publish_task = self.mid_server.publish_strategy_instance_param(params[0].strategy_instance_id)
                    self.background_task_manager.submit_task(
                        publish_task,
                        f"publish_strategy_instance_param_{params[0].strategy_instance_id}"
                    )
                    
                return result
            except Exception as e:
                flogger.error("update instance param failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"update instance param failed: {str(e)}"
                )

        @self.app.get("/py_strategy/start_instance", response_model=HttpResponseModel)
        async def start_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("start instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_start_strategy_instance(trading_account_id, instance_ids)

                # 关键改动：使用后台任务执行WebSocket推送
                if result.code == 0:
                    publish_task = self.mid_server.publish_strategy_instance(instance_ids)
                    self.background_task_manager.submit_task(
                        publish_task,
                        f"publish_strategy_instance_start_{instance_ids}"
                    )

                return result
            except Exception as e:
                flogger.error("start instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"start instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/pause_instance", response_model=HttpResponseModel)
        async def pause_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("pause instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response

                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_pause_strategy_instance(trading_account_id, instance_ids)

                # 关键改动：使用后台任务执行WebSocket推送
                if result.code == 0:
                    publish_task = self.mid_server.publish_strategy_instance(instance_ids)
                    self.background_task_manager.submit_task(
                        publish_task,
                        f"publish_strategy_instance_pause_{instance_ids}"
                    )

                return result
            except Exception as e:
                flogger.error("pause instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"pause instance failed: {str(e)}"
                )

        # WebSocket路由
        @self.app.websocket("/")
        async def websocket_endpoint(websocket: WebSocket):
            if self.mid_server:
                await self.mid_server.get_websocket_server().websocket_endpoint(websocket)

    def start(self) -> None:
        """启动策略中台服务"""
        flogger.info("starting py strategy mid server")

        # 启动后台任务管理器
        self.background_task_manager.start()

        # 启动中台服务
        try:
            self.mid_server = StrategyMidServer(
                mdb=self.mdb,
                strategy_engines=self.config.strategy_engines,
            )
            # 同步启动，不使用await
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.mid_server.start())
            flogger.info("strategy mid server started")
        except Exception as e:
            flogger.error("strategy mid server start failed", error=str(e))
            raise

        # 启动HTTP服务器
        try:
            self.http_server = HttpServer(
                self.app,
                self.config.mid_server.host,
                self.config.mid_server.http_port
            )
            self.http_server.start()
            flogger.info("http server started")
        except Exception as e:
            flogger.error("http server start failed", error=str(e))
            if self.mid_server:
                self.stop()
            raise

        # 启动WebSocket服务器
        try:
            self.ws_server = WebSocketServer(
                self.app,
                self.config.mid_server.host,
                self.config.mid_server.ws_port
            )
            self.ws_server.start()
            flogger.info("websocket server started")
        except Exception as e:
            flogger.error("websocket server start failed", error=str(e))
            if self.http_server:
                self.http_server.stop()
            if self.mid_server:
                self.stop()
            raise

        self.running = True
        flogger.info("py strategy mid server started success", active_status=self.running)

    def stop(self) -> None:
        """停止策略中台服务"""
        flogger.info("stopping py strategy mid server")

        # 停止后台任务管理器
        try:
            self.background_task_manager.stop()
        except Exception as e:
            flogger.error("background task manager stop failed", error=str(e))

        try:
            if self.http_server:
                self.http_server.stop()
                flogger.info("http server stopped")
        except Exception as e:
            flogger.error("http server stop failed", error=str(e))

        try:
            if self.ws_server:
                self.ws_server.stop()
                flogger.info("websocket server stopped")
        except Exception as e:
            flogger.error("websocket server stop failed", error=str(e))

        try:
            if self.mid_server:
                import asyncio
                loop = asyncio.get_event_loop()
                loop.run_until_complete(self.mid_server.stop())
                flogger.info("strategy mid server stopped")
        except Exception as e:
            flogger.error("strategy mid server stop failed", error=str(e))

        self.running = False
        flogger.info("py strategy mid server stopped success", active_status=self.running)


def signal_handler(signum: int, frame: Any) -> None:
    flogger.info("signal received", signum=signum)
    if main and main.running:
        main.stop()
    flogger.info("exit py strategy mid server")
    sys.exit(0)


if __name__ == "__main__":
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建并启动主程序
    main = Main()
    main.start()

    # 主循环
    try:
        while main.running:
            time.sleep(1)
    except KeyboardInterrupt:
        main.stop()
