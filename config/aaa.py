#!/usr/bin/env python3
"""
演示后台异步任务的工作原理
"""

import asyncio
import time
import threading
import queue
from utils.flog import flogger


class BackgroundTaskManager:
    """后台任务管理器 - 演示版本"""
    
    def __init__(self):
        self.task_queue = queue.Queue()
        self.worker_thread = None
        self.running = False
        self.loop = None
        
    def start(self):
        """启动后台任务处理器"""
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        print("✅ 后台任务管理器启动")
        
    def stop(self):
        """停止后台任务处理器"""
        self.running = False
        # 添加停止信号
        self.task_queue.put(None)
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
        print("🛑 后台任务管理器停止")
        
    def submit_task(self, coro, task_name: str = "unknown"):
        """提交异步任务到后台执行"""
        if self.running:
            self.task_queue.put((coro, task_name))
            print(f"📤 任务提交到后台: {task_name}")
        else:
            print(f"⚠️  后台任务管理器未运行，任务被忽略: {task_name}")
            
    def _worker(self):
        """后台工作线程"""
        print("🔄 后台工作线程启动")
        
        # 为这个线程创建新的事件循环
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            while self.running:
                try:
                    # 从队列获取任务，超时1秒
                    task_item = self.task_queue.get(timeout=1)
                    
                    # 检查停止信号
                    if task_item is None:
                        break
                        
                    coro, task_name = task_item
                    
                    # 执行异步任务
                    try:
                        print(f"🚀 开始执行后台任务: {task_name}")
                        start_time = time.time()
                        self.loop.run_until_complete(coro)
                        end_time = time.time()
                        print(f"✅ 后台任务完成: {task_name} (耗时: {end_time - start_time:.2f}秒)")
                    except Exception as e:
                        print(f"❌ 后台任务失败: {task_name}, 错误: {str(e)}")
                    finally:
                        self.task_queue.task_done()
                        
                except queue.Empty:
                    # 超时，继续循环
                    continue
                except Exception as e:
                    print(f"❌ 后台工作线程错误: {str(e)}")
                    
        finally:
            # 清理事件循环
            try:
                self.loop.close()
            except Exception as e:
                print(f"❌ 关闭事件循环失败: {str(e)}")
                
        print("🔄 后台工作线程停止")


# 模拟异步任务
async def simulate_websocket_publish(instance_id: int, delay: float = 1.0):
    """模拟WebSocket发布任务"""
    print(f"  📡 开始WebSocket发布 (instance_id: {instance_id})")
    await asyncio.sleep(delay)  # 模拟网络延迟
    print(f"  📡 WebSocket发布完成 (instance_id: {instance_id})")


async def simulate_http_request_handler(instance_id: int, task_manager: BackgroundTaskManager):
    """模拟HTTP请求处理"""
    print(f"\n🌐 HTTP请求开始处理 (instance_id: {instance_id})")
    
    # 模拟业务逻辑处理
    await asyncio.sleep(0.1)  # 模拟数据库操作等
    print(f"💾 业务逻辑处理完成 (instance_id: {instance_id})")
    
    # 提交后台任务 - 关键点：不等待完成
    publish_task = simulate_websocket_publish(instance_id, delay=2.0)
    task_manager.submit_task(publish_task, f"websocket_publish_{instance_id}")
    
    # HTTP立即返回响应
    print(f"🌐 HTTP响应立即返回 (instance_id: {instance_id})")
    return {"code": 0, "message": "success", "instance_id": instance_id}


def demo_background_tasks():
    """演示后台任务的工作流程"""
    print("=" * 60)
    print("🎯 后台异步任务演示")
    print("=" * 60)
    
    # 创建后台任务管理器
    task_manager = BackgroundTaskManager()
    task_manager.start()
    
    try:
        # 创建事件循环来模拟FastAPI环境
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        print("\n📋 模拟场景：连续处理3个HTTP请求")
        print("-" * 40)
        
        # 模拟连续的HTTP请求
        async def simulate_requests():
            tasks = []
            for i in range(1, 4):
                print(f"\n⏰ 时间点 {i}: 收到HTTP请求")
                task = simulate_http_request_handler(i, task_manager)
                tasks.append(task)
                
                # 稍微间隔一下
                await asyncio.sleep(0.5)
            
            # 等待所有HTTP请求处理完成
            results = await asyncio.gather(*tasks)
            print(f"\n📊 所有HTTP请求处理完成: {results}")
            
        # 运行模拟
        loop.run_until_complete(simulate_requests())
        
        # 等待后台任务完成
        print(f"\n⏳ 等待后台任务完成...")
        time.sleep(8)  # 给后台任务足够时间完成
        
    finally:
        # 清理
        task_manager.stop()
        loop.close()
        
    print("\n" + "=" * 60)
    print("🎯 演示完成")
    print("=" * 60)


def demo_comparison():
    """对比同步vs异步执行的差异"""
    print("\n" + "=" * 60)
    print("⚖️  同步 vs 异步执行对比")
    print("=" * 60)
    
    # 同步方式
    print("\n🔄 同步方式 (等待WebSocket发布完成):")
    start_time = time.time()
    
    async def sync_way():
        print("  🌐 HTTP请求开始")
        await asyncio.sleep(0.1)  # 业务逻辑
        print("  💾 业务逻辑完成")
        await simulate_websocket_publish(1, delay=2.0)  # 等待WebSocket发布
        print("  🌐 HTTP响应返回")
        
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(sync_way())
    loop.close()
    
    sync_time = time.time() - start_time
    print(f"  ⏱️  总耗时: {sync_time:.2f}秒")
    
    # 异步方式
    print(f"\n🚀 异步方式 (后台执行WebSocket发布):")
    start_time = time.time()
    
    task_manager = BackgroundTaskManager()
    task_manager.start()
    
    try:
        async def async_way():
            print("  🌐 HTTP请求开始")
            await asyncio.sleep(0.1)  # 业务逻辑
            print("  💾 业务逻辑完成")
            # 提交后台任务，不等待
            publish_task = simulate_websocket_publish(1, delay=2.0)
            task_manager.submit_task(publish_task, "websocket_publish_1")
            print("  🌐 HTTP响应立即返回")
            
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(async_way())
        loop.close()
        
        async_time = time.time() - start_time
        print(f"  ⏱️  HTTP响应耗时: {async_time:.2f}秒")
        
        # 等待后台任务完成
        print("  ⏳ 后台任务继续执行中...")
        time.sleep(3)
        
    finally:
        task_manager.stop()
    
    print(f"\n📈 性能提升: {((sync_time - async_time) / sync_time * 100):.1f}%")


if __name__ == "__main__":
    demo_background_tasks()
    demo_comparison()
