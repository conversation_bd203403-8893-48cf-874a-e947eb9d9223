import signal
import sys
import time
import threading
from typing import Optional, Any, List
from fastapi import FastAP<PERSON>, WebSocket, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from strategy_mid_server.mid_server import StrategyMidServer
from common.mdb import MemoryDatabase
from common.models import *
from utils.flog import flogger
from utils.csv_converter import CSVConverter
from config.config import ConfigManager


class HttpServer:
    """HTTP服务器类，运行在独立线程中"""
    
    def __init__(self, app: FastAPI, host: str, port: int):
        self.app = app
        self.host = host
        self.port = port
        self.thread = None
        
    def start(self):
        flogger.info("starting http server", host=self.host, port=self.port)
        self.thread = threading.Thread(target=self._run, name="HttpServer")
        self.thread.daemon = True
        self.thread.start()
        flogger.info("http server started", host=self.host, port=self.port)
        
    def _run(self):
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info",
            access_log=False
        )
        
    def stop(self):
        flogger.info("http server stopped")


class WebSocketServer:
    """WebSocket服务器类，运行在独立线程中"""
    
    def __init__(self, app: FastAPI, host: str, port: int):
        self.app = app
        self.host = host
        self.port = port
        self.thread = None
        
    def start(self):
        flogger.info("starting websocket server", host=self.host, port=self.port)
        self.thread = threading.Thread(target=self._run, name="WebSocketServer")
        self.thread.daemon = True
        self.thread.start()
        flogger.info("websocket server started", host=self.host, port=self.port)
        
    def _run(self):
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info",
            access_log=False
        )
        
    def stop(self):
        flogger.info("websocket server stopped")


class Main:
    """策略中台主应用类"""

    def __init__(self):
        config_file = "../config/py_strategy_mid_config.json"
        self.config = ConfigManager.get_instance(config_file).get_config()
        flogger.info("init py strategy mid server", config=self.config)
        
        # 组件初始化
        self.mdb = MemoryDatabase()
        self.mid_server: Optional[StrategyMidServer] = None
        self.app: Optional[FastAPI] = None
        self.http_server: Optional[HttpServer] = None
        self.ws_server: Optional[WebSocketServer] = None
        self.running = False

        # 设置应用
        self._setup_app()
        flogger.info("py strategy mid server init complete")

    def _check_mid_server_ready(self) -> Optional[HttpResponseModel]:
        if not self.mid_server:
            return HttpResponseModel(
                data=[],
                code=-1,
                message="mid server not initialized"
            )
        return None

    def _setup_app(self):
        """设置FastAPI应用"""
        self.app = FastAPI(
            title="py_strategy_mid",
            description="manage python strategy",
            version="1.0.0"
        )

        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self._setup_routes()

    def _setup_routes(self):
        """设置所有路由"""
        @self.app.get("/heartbeat")
        async def heartbeat():
            """心跳检查"""
            if self.mid_server and self.running:
                return HeartbeatResponseModel(code=0, message="success")
            else:
                return HeartbeatResponseModel(code=-1, message="failed")

        @self.app.post("/py_strategy/create_instance", response_model=HttpResponseModel)
        async def create_strategy_instance(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("create strategy instance request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                strategy_instance = CSVConverter.parse_csv_to_model(request_data, StrategyInstanceMsg)
                result = await self.mid_server.handle_create_strategy_instance(strategy_instance)
                if result.code == 0:
                    # 简化：不使用后台任务，直接调用
                    try:
                        await self.mid_server.publish_strategy_instance(strategy_instance.strategy_instance_id)
                    except Exception as e:
                        flogger.error("publish strategy instance failed", error=str(e))
                return result
            except Exception as e:
                flogger.error("create strategy instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"create instance failed: {str(e)}"
                )

        @self.app.post("/py_strategy/update_instance_param", response_model=HttpResponseModel)
        async def update_instance_param(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("update instance param request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                params: List[StrategyInstanceParamMsg] = []
                lines = request_data.strip().split('\n')
                if not lines:
                    return HttpResponseModel(
                        data=[],
                        code=-1,
                        message="empty message"
                    )
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        param = CSVConverter.parse_csv_to_model(line, StrategyInstanceParamMsg)
                        params.append(param)
                    except Exception as e:
                        flogger.error("parse csv line failed",
                                     line_num=line_num,
                                     line_data=line,
                                     error=str(e))
                        return HttpResponseModel(
                            data=[],
                            code=-1,
                            message=f"parse csv line {line_num} failed: {str(e)}"
                        )
                result = await self.mid_server.handle_update_instance_param(params)
                if result.code == 0:
                    try:
                        await self.mid_server.publish_strategy_instance_param(params[0].strategy_instance_id)
                    except Exception as e:
                        flogger.error("publish strategy instance param failed", error=str(e))
                return result
            except Exception as e:
                flogger.error("update instance param failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"update instance param failed: {str(e)}"
                )

        @self.app.post("/py_strategy/modify_instance_priority", response_model=HttpResponseModel)
        async def modify_instance_priority(request: Request):
            try:
                body = await request.body()
                request_data = body.decode('utf-8')
                flogger.info("modify instance priority request", data=request_data)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                strategy_instance = CSVConverter.parse_csv_to_model(request_data, StrategyInstanceMsg)
                result = await self.mid_server.handle_modify_instance_priority(strategy_instance)
                if result.code == 0:
                    try:
                        await self.mid_server.publish_strategy_instance(strategy_instance.strategy_instance_id)
                    except Exception as e:
                        flogger.error("publish strategy instance failed", error=str(e))
                return result
            except Exception as e:
                flogger.error("modify instance priority failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"modify instance priority failed: {str(e)}"
                )

        @self.app.get("/py_strategy/delete_instance", response_model=HttpResponseModel)
        async def delete_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("delete instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_delete_strategy_instance(trading_account_id, instance_ids)
                if result.code == 0:
                    try:
                        await self.mid_server.publish_strategy_instance(instance_ids)
                    except Exception as e:
                        flogger.error("publish strategy instance failed", error=str(e))
                return result
            except Exception as e:
                flogger.error("delete instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"delete instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/start_instance", response_model=HttpResponseModel)
        async def start_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("start instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response
                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_start_strategy_instance(trading_account_id, instance_ids)
                if result.code == 0:
                    try:
                        await self.mid_server.publish_strategy_instance(instance_ids)
                    except Exception as e:
                        flogger.error("publish strategy instance failed", error=str(e))
                return result
            except Exception as e:
                flogger.error("start instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"start instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/pause_instance", response_model=HttpResponseModel)
        async def pause_instance(trading_account_id: int, strategy_instance_id_list: str):
            try:
                flogger.info("pause instance request",
                           trading_account_id=trading_account_id,
                           strategy_instance_id_list=strategy_instance_id_list)
                error_response = self._check_mid_server_ready()
                if error_response:
                    return error_response

                instance_ids = [int(id_str.strip()) for id_str in strategy_instance_id_list.split(';') if id_str.strip()]
                result = await self.mid_server.handle_pause_strategy_instance(trading_account_id, instance_ids)

                if result.code == 0:
                    try:
                        await self.mid_server.publish_strategy_instance(instance_ids)
                    except Exception as e:
                        flogger.error("publish strategy instance failed", error=str(e))
                return result
            except Exception as e:
                flogger.error("pause instance failed", error=str(e))
                return HttpResponseModel(
                    data=[],
                    code=-1,
                    message=f"pause instance failed: {str(e)}"
                )

        @self.app.get("/py_strategy/req_tablepublisher")
        async def req_tablepublisher():
            """获取表发布信息"""
            try:
                if not self.mid_server:
                    default_response = TablePublisherResponseModel()
                    return [default_response.model_dump()]
                table_publisher_info = self.mid_server.get_table_publisher_info()
                return [table_publisher_info.model_dump()]
            except Exception as e:
                flogger.error("get table publisher info failed", error=str(e))
                default_response = TablePublisherResponseModel()
                return [default_response.model_dump()]

        # WebSocket路由
        @self.app.websocket("/")
        async def websocket_endpoint(websocket: WebSocket):
            if self.mid_server:
                await self.mid_server.get_websocket_server().websocket_endpoint(websocket)

    def start(self) -> None:
        """启动策略中台服务"""
        flogger.info("starting py strategy mid server")

        # 启动中台服务
        try:
            self.mid_server = StrategyMidServer(
                mdb=self.mdb,
                strategy_engines=self.config.strategy_engines,
            )
            # 同步启动，不使用await
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.mid_server.start())
            flogger.info("strategy mid server started")
        except Exception as e:
            flogger.error("strategy mid server start failed", error=str(e))
            raise

        # 启动HTTP服务器
        try:
            self.http_server = HttpServer(
                self.app,
                self.config.mid_server.host,
                self.config.mid_server.http_port
            )
            self.http_server.start()
            flogger.info("http server started")
        except Exception as e:
            flogger.error("http server start failed", error=str(e))
            if self.mid_server:
                self.stop()
            raise

        # 启动WebSocket服务器
        try:
            self.ws_server = WebSocketServer(
                self.app,
                self.config.mid_server.host,
                self.config.mid_server.ws_port
            )
            self.ws_server.start()
            flogger.info("websocket server started")
        except Exception as e:
            flogger.error("websocket server start failed", error=str(e))
            if self.http_server:
                self.http_server.stop()
            if self.mid_server:
                self.stop()
            raise

        self.running = True
        flogger.info("py strategy mid server started success", active_status=self.running)

    def stop(self) -> None:
        """停止策略中台服务"""
        flogger.info("stopping py strategy mid server")

        try:
            if self.http_server:
                self.http_server.stop()
                flogger.info("http server stopped")
        except Exception as e:
            flogger.error("http server stop failed", error=str(e))

        try:
            if self.ws_server:
                self.ws_server.stop()
                flogger.info("websocket server stopped")
        except Exception as e:
            flogger.error("websocket server stop failed", error=str(e))

        try:
            if self.mid_server:
                import asyncio
                loop = asyncio.get_event_loop()
                loop.run_until_complete(self.mid_server.stop())
                flogger.info("strategy mid server stopped")
        except Exception as e:
            flogger.error("strategy mid server stop failed", error=str(e))

        self.running = False
        flogger.info("py strategy mid server stopped success", active_status=self.running)


def signal_handler(signum: int, frame: Any) -> None:
    flogger.info("signal received", signum=signum)
    if main and main.running:
        main.stop()
    flogger.info("exit py strategy mid server")
    sys.exit(0)


if __name__ == "__main__":
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建并启动主程序
    main = Main()
    main.start()

    # 主循环
    try:
        while main.running:
            time.sleep(1)
    except KeyboardInterrupt:
        main.stop()
